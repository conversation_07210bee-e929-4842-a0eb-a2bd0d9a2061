from services.processors.base_processor import DocumentProcessor
from utils.openai_client import OpenAIClient
from utils.langchain_client import LangChainClient
from fastapi import UploadFile, HTTPException
from typing import Optional, List, Dict, Any
from pydantic import BaseModel  # Used in type hints
import os
import tempfile
import fitz
import base64
import requests
from domain.models.facturius.invoice_model import (
    InvoicePurchase,
    InvoiceElectric,
    InvoiceWater,
    InvoiceGas,
)
from utils.helpers import convert_excel_to_csv, convert_xls_to_csv
import gc
import asyncio  # Used for CancelledError handling
from copy import deepcopy
import re

xls_prompt_text = """
        Extract all relevant information from the provided CSV data and present it in a standardized format, without adding any notes or additional information.
        If the information is missing, do NOT make up new info; leave it blank.

        Invoice Details:
        - Invoice Number: [Value]
        - Invoice Type: [Choose one of: 'Delivery ticket', 'Daily job tracking form', 'Credit Note', 'Purchase', 'Other']
        - Billing_Date: [Value] (if there is no due date leave it null, keep the original date format)
        - Due Date: [Value] (if there is no due date leave it empty)
        - Account Number: [Value]
        - Total Amount Due: [Value]
        - Paid Amount: [Value]
        - Outstanding Balance: [Value]
        - Billing name: [Value]

        Vendor/Issued By:
        - name: [Value]
        - Address: [Value]
        - Telephone: [Value]
        - Fax: [Value]

        Total details:
        - Subtotal: [Value] (Pre-tax total before taxes and additional charges)
        - Taxes: [Value] (Sum ALL individual tax amounts - city, state, sales tax, etc.)
        - Discount: [Value]
        - Shipping Charges: [Value] (Freight, delivery, crate charges)
        - Additional Charges: [Value] (Materials inflation surcharge, service fees)
        - Literal Total: [Value] (ONLY if there's a clearly labeled total like "Amount Due", "Total", "Balance Due" - extract exact value, do NOT calculate)
        - Total amount: [Value] (Final amount due - the amount customer must pay)

        Additional Information (only if there is relevant information list it in here, naming each field acordingly):
        - [Label]: [Value]

        (Continue listing fields as needed based on the information presented on the invoice.)

        Items List:
        1. Item: [Description]
        - Quantity: [Value]
        - Unit Price: [Value]
        - Total Price: [Value]

        (Continue numbering for each item.)

        Instructions:
        + All dates must be in MM/DD/YYYY format
        + Identify the type of bill or invoice from the list and extract all key information, including the issuer, dates, and total amount due.
        + Extract additional relevant fields such as payment terms, service dates, or any breakdown of charges that are included in the invoice.
        + Include every item from the invoice's 'Items List' without omission, even if items appear similar.
        + Extract accurately all specified fields, paying special attention to the Account Summary in service invoices.
        + Do not add any notes or additional comments; only include the information extracted from the invoice.
        + Present all information clearly and in the order shown above.
        + If there is a field missing from the invoice leave it blank

        """

# Standard prompt for invoice image analysis
prompt = """
You will analyze this invoice image.
Extract all structured information according to the provided model.

IMPORTANT INSTRUCTIONS FOR MULTI-PAGE INVOICES:
1. For the invoice_number field:
   - Extract ONLY the actual invoice identifier (e.g., INV-12345, 987654)
   - DO NOT use page numbers like 'PAGE 2 OF 4' as invoice numbers
   - DO NOT use placeholders like 'N/A' or 'Unknown' as invoice numbers
   - If this appears to be a continuation page without its own invoice number, leave the invoice_number field EMPTY ("")
   - If you see "Credit Note Number" instead of "Invoice Number", use that value and set invoice_type to "Credit Note"

2. For continuation pages:
   - If this is clearly a continuation of a previous invoice page, set description to "Continuation page"
   - Focus on extracting any additional line items or information not present on previous pages

3. For invoice_type field:
   - Use ONLY one of these valid types: 'Delivery ticket', 'Daily job tracking form', 'Credit Note', 'Purchase', 'Other'
   - IMPORTANT: If you see ANY indication this is a Credit Note (such as "Credit Note Number", "Credit Memo", etc.),
     set invoice_type to "Credit Note" even if the document has "Invoice" in the title
   - For Credit Notes, ensure ALL monetary values (including taxes) keep being negative if they already are negative.
   - If this page is a delivery ticket, set invoice_type to "Delivery ticket"
   - If this page is a daily job tracking form, set invoice_type to "Daily job tracking form"
   - The system will only use the page number of delivery tickets and daily job tracking forms, not their content
   - Make sure to correctly identify these document types vs. regular invoices

4. For all fields:
   - For float fields, if the value is null, use 0.0 instead
   - Extract all visible information even if incomplete
   - Do not make up or infer information not present in the image

5. For total amount fields (CRITICAL):
   - partial_total: Extract the pre-tax subtotal (look for "Pre-Tax Total", "Subtotal")
   - Taxes: Sum ALL individual tax amounts (city tax + state tax + sales tax, etc.)
   - shipping_charges: Extract freight/shipping costs (look for "Freight", "Crate Charge")
   - additional_charges: Extract surcharges (look for "Materials Inflation Surcharge")
   - literal_total: ONLY extract if there's a clearly labeled total like "Amount Due: 4,205.96" - extract the exact value next to these labels. Do NOT calculate or sum anything. If no clear label exists, leave as null.
   - final_total: Extract the final amount due (look for "Amount Due" - this is usually the largest number)
   - ALWAYS ensure final_total reflects the actual total amount the customer must pay

Focus on identifying key details such as 'invoice_number', 'issued_by', 'billing_date',
'due_date', 'total_amount_due', 'items_list', and any specific charges relevant to the invoice type.

IMPORTANT REMINDERS:
- It's better to leave the invoice_number field empty for continuation pages than to use page numbers or placeholders.
- Always use one of the valid invoice types: 'Delivery ticket', 'Daily job tracking form', 'Credit Note', 'Purchase', or 'Other'.
- Correctly identifying the invoice type is crucial for proper processing.
- Invoice numbers can appear sometimes as Account number
- In invoice numbers ONLY extract the actual invoice identifier, do not include 'Invoice number: ', or any other text.
- For Credit Notes, ensure ALL monetary values (including taxes) keep being negative if they already are negative.
"""

class InvoiceProcessorTest(DocumentProcessor):
    """
    Test processor for handling and extracting information from various types of invoices.

    This version uses direct image-to-structured-data extraction and merges related invoices.

    Key differences from the standard processor:
    - Uses get_structured_data_image to directly extract structured data from images
    - Merges JSONs without invoice numbers with the previous one that has an invoice number
    - Merges JSONs with the same invoice number

    Attributes:
        openai_client (OpenAIClient): Client for OpenAI API interactions
        langchain_client (LangChainClient): Client for LangChain operations
        page_only_document_types (set): Document types that only contribute page numbers, not content
    """

    # Define document types that should only contribute page numbers, not content
    PAGE_ONLY_DOCUMENT_TYPES = {
        'delivery ticket',
        'daily job tracking form'
    }

    def __init__(self, openai_client: OpenAIClient, langchain_client: LangChainClient):
        self.openai_client = openai_client
        self.langchain_client = langchain_client
        self.page_only_document_types = self.PAGE_ONLY_DOCUMENT_TYPES.copy()

    def is_page_only_document(self, invoice_type: str) -> bool:
        """
        Check if a document type should only contribute page numbers, not content.

        Args:
            invoice_type (str): The invoice type to check

        Returns:
            bool: True if this document type should only contribute page numbers
        """
        if not invoice_type:
            return False
        return invoice_type.lower().strip() in self.page_only_document_types

    def add_page_only_document_type(self, document_type: str) -> None:
        """
        Add a new document type that should only contribute page numbers.

        Args:
            document_type (str): The document type to add (will be converted to lowercase)
        """
        self.page_only_document_types.add(document_type.lower().strip())

    async def process(self, file: Optional[UploadFile], data: Optional[str] = None) -> dict:
        """
        Main entry point for invoice processing.

        Args:
            file (Optional[UploadFile]): File to process
            data (Optional[str]): URL to download file from

        Returns:
            dict: Processed invoice data

        Raises:
            HTTPException: If neither file nor URL is provided
        """
        if not file and not data:
            raise HTTPException(status_code=400, detail="The Invoice action requires a file or URL")
        result = await self.process_invoice(file, data)
        return result

    async def process_invoice(self, file: UploadFile, data: Optional[str] = None) -> dict:
        """
        Process invoice file or URL and extract structured data.

        This version directly extracts structured data from images and merges related invoices.

        Args:
            file (UploadFile): File to process
            data (Optional[str]): URL to download file from

        Returns:
            dict: List of structured invoice data

        Raises:
            HTTPException: For various processing errors
            asyncio.CancelledError: If the task is cancelled
        """
        # Track temporary files for cleanup in case of cancellation
        temp_files = []

        with tempfile.TemporaryDirectory() as temp_dir:
            try:
                if data:
                    # Download file from URL
                    url = data.strip()
                    response = requests.get(url)
                    if response.status_code != 200:
                        raise HTTPException(status_code=400, detail="It could not download the file from the URL")
                    # Get filename from URL
                    filename = url.split("/")[-1].split("?")[0]
                    file_extension = filename.split(".")[-1].lower()
                    file_content = response.content
                else:
                    # Use uploaded file
                    filename = file.filename
                    file_extension = filename.split(".")[-1].lower()
                    file_content = await file.read()

                image_extensions = ['png', 'jpg', 'jpeg', 'tiff', 'bmp']

                # List to store structured data from each page
                page_data_list = []

                if file_extension == 'pdf':
                    # Save PDF to temp directory
                    pdf_path = os.path.join(temp_dir, filename)
                    with open(pdf_path, 'wb') as f:
                        f.write(file_content)
                    # Track the temporary file
                    temp_files.append(pdf_path)

                    # Open PDF and process each page
                    with fitz.open(pdf_path) as doc:
                        for page_num, page in enumerate(doc):
                            # Render page to image
                            pix = page.get_pixmap(dpi=300)
                            image_filename = f"page-{page_num}.png"
                            image_path = os.path.join(temp_dir, image_filename)
                            pix.save(image_path)
                            # Track the temporary file
                            temp_files.append(image_path)

                            # Process image directly to structured data
                            try:
                                # Determine invoice type (simplified for direct processing)
                                invoice_type = "Purchase Invoice"  # Default type
                                InvoiceModel = self.select_invoice_model(invoice_type)

                                # Encode the image to base64
                                base64_image_data = self.encode_image(image_path)

                                # Extract structured data directly from image using the LangChainClient helper
                                response = await self.langchain_client.get_structured_data_image(
                                    structure=InvoiceModel,
                                    base64_image_data=base64_image_data,
                                    prompt=prompt
                                )
                                page_data = response['response']

                                # Store token usage for this page
                                token_usage = response['token_usage']

                                # Add page number for reference
                                page_data["page_number"] = page_num + 1

                                # Add token usage to page data
                                page_data["token_usage"] = {
                                    "prompt_tokens": token_usage["prompt_tokens"],
                                    "completion_tokens": token_usage["completion_tokens"],
                                    "total_tokens": token_usage["total_tokens"],
                                    "cost": token_usage["cost"]
                                }

                                # Add to list
                                page_data_list.append(page_data)

                            except Exception as e:
                                print(f"Error processing page {page_num + 1}: {str(e)}")
                                # Continue with next page even if this one fails

                            # Clean up
                            os.remove(image_path)
                            gc.collect()

                    # Clean up PDF
                    os.remove(pdf_path)

                elif file_extension in image_extensions:
                    # Save image to temp directory
                    image_path = os.path.join(temp_dir, filename)
                    with open(image_path, 'wb') as f:
                        f.write(file_content)
                    # Track the temporary file
                    temp_files.append(image_path)

                    # Process single image
                    try:
                        # Default to purchase invoice type
                        invoice_type = "Purchase Invoice"
                        InvoiceModel = self.select_invoice_model(invoice_type)

                        # Encode the image to base64
                        base64_image_data = self.encode_image(image_path)

                        # Extract structured data directly from image using the LangChainClient helper
                        response = await self.langchain_client.get_structured_data_image(
                            structure=InvoiceModel,
                            base64_image_data=base64_image_data,
                            prompt=prompt
                        )
                        page_data = response['response']

                        # Store token usage for this page
                        token_usage = response['token_usage']

                        # Add page number
                        page_data["page_number"] = 1

                        # Add token usage to page data
                        page_data["token_usage"] = {
                            "prompt_tokens": token_usage["prompt_tokens"],
                            "completion_tokens": token_usage["completion_tokens"],
                            "total_tokens": token_usage["total_tokens"],
                            "cost": token_usage["cost"]
                        }

                        # Add to list
                        page_data_list.append(page_data)

                    except Exception as e:
                        raise HTTPException(status_code=500, detail=f"Error processing image: {str(e)}")

                    # Clean up
                    os.remove(image_path)

                elif file_extension in ['xls', 'xlsx', 'csv']:
                    # Save the file in the temporary directory`
                    csv_tot_text = ''
                    sheet_path = os.path.join(temp_dir, filename)
                    with open(sheet_path, 'wb') as f:
                        f.write(file_content)
                    try:
                        if file_extension == 'csv':
                            with open(sheet_path, "r", encoding="utf-8") as file:
                                csv_content = file.read()

                            os.remove(sheet_path)
                            prompt_with_csv = f"{xls_prompt_text}\nCSV Content:\n{csv_content}"

                            max_retries = 3
                            for attempt in range(max_retries):
                                try:
                                    response = await self.langchain_client.simple_invoke(prompt_with_csv)
                                    csv_text_content = response
                                    # Append the result to invoices_text_list
                                    invoices_text_list.append(csv_text_content)
                                    break
                                except Exception as e:
                                    if attempt < max_retries - 1:
                                        print("Rate limit reached, waiting 40 seconds before retrying...")
                                        await asyncio.sleep(40)
                                    else:
                                        raise HTTPException(status_code=429, detail="Rate limit reached, please try again later.")

                        elif file_extension == 'xls':
                            # Use convert_xls_to_csv
                            try:
                                csv_contents = convert_xls_to_csv(sheet_path)
                                os.remove(sheet_path)
                            finally:
                                gc.collect()  # Force garbage collection after processing large files
                        elif file_extension == 'xlsx':
                            # Use convert_excel_to_csv
                            try:
                                csv_contents = convert_excel_to_csv(sheet_path)
                                os.remove(sheet_path)
                            finally:
                                gc.collect()  # Force garbage collection after processing large files
                        else:
                            raise HTTPException(status_code=400, detail="Unsupported file format.")

                        if file_extension in ['xls', 'xlsx']:
                            sheet_num = 0
                            for csv_content in csv_contents:
                                # Combine the prompt with the CSV content
                                prompt_with_csv = f"{xls_prompt_text}\nCSV Content:\n{csv_content}"
                                # Call OpenAI to process the CSV content
                                max_retries = 3
                                for attempt in range(max_retries):
                                    try:
                                        sheet_num += 1
                                        response = await self.langchain_client.simple_invoke(prompt_with_csv)
                                        csv_text_content = response
                                        # Append the result to invoices_text_list
                                        csv_tot_text += csv_text_content
                                        csv_tot_text += f"<<PageNumber: {sheet_num}>>"
                                        break
                                    except Exception as e:
                                        if attempt < max_retries - 1:
                                            print("Rate limit reached, waiting 40 seconds before retrying...")
                                            await asyncio.sleep(40)
                                        else:
                                            raise HTTPException(status_code=429, detail="Rate limit reached, please try again later.")

                            invoices_text_list = self.split_invoices(csv_tot_text)
                            all_invoice_data = []

                            for invoice_text in invoices_text_list:
                                # Determinar el tipo de factura a partir del texto obtenido
                                invoice_type = self.extract_invoice_type(invoice_text)

                                # Seleccionar el modelo adecuado según el tipo de factura
                                InvoiceModel = self.select_invoice_model(invoice_type)

                                # Realizar la segunda llamada a OpenAI para obtener datos estructurados
                                response = await self.get_structured_data(invoice_text, InvoiceModel)
                                invoice_data = response['response']

                                # Store token usage for this invoice
                                token_usage = response['token_usage']

                                # Add token usage to invoice data
                                invoice_data["token_usage"] = {
                                    "prompt_tokens": token_usage["prompt_tokens"],
                                    "completion_tokens": token_usage["completion_tokens"],
                                    "total_tokens": token_usage["total_tokens"],
                                    "cost": token_usage["cost"]
                                }

                                # Añadir los datos de la factura a la lista
                                all_invoice_data.append(invoice_data)

                            # Add extraction_cost field to each invoice
                            for invoice in all_invoice_data:
                                if 'token_usage' in invoice:
                                    # Add extraction_cost field with token usage details
                                    invoice['extraction_cost'] = {
                                        'tokens': {
                                            'prompt_tokens': invoice['token_usage']['prompt_tokens'],
                                            'completion_tokens': invoice['token_usage']['completion_tokens'],
                                            'total_tokens': invoice['token_usage']['total_tokens']
                                        },
                                        'cost_usd': invoice['token_usage']['cost']
                                    }

                                    # Remove the internal token_usage field
                                    del invoice['token_usage']

                            # Devolver la lista de datos estructurados
                            return all_invoice_data

                    except Exception as e:
                        raise HTTPException(status_code=500, detail=f"Error processing CSV or Excel file: {str(e)}")
                else:
                    raise HTTPException(status_code=400, detail="The file must be a PDF or image.")

                # Merge related invoices
                merged_invoices = self.merge_related_invoices(page_data_list)

                # Add extraction_cost field to each invoice
                for invoice in merged_invoices:
                    if 'token_usage' in invoice:
                        # Add extraction_cost field with token usage details
                        invoice['extraction_cost'] = {
                            'tokens': {
                                'prompt_tokens': invoice['token_usage']['prompt_tokens'],
                                'completion_tokens': invoice['token_usage']['completion_tokens'],
                                'total_tokens': invoice['token_usage']['total_tokens']
                            },
                            'cost_usd': invoice['token_usage']['cost']
                        }

                        # Remove the internal token_usage field
                        del invoice['token_usage']

                return merged_invoices

            except asyncio.CancelledError:
                # Clean up any temporary files that might still exist
                for temp_file in temp_files:
                    if os.path.exists(temp_file):
                        try:
                            os.remove(temp_file)
                        except Exception as cleanup_error:
                            print(f"Error cleaning up file {temp_file}: {cleanup_error}")
                # Re-raise to propagate the cancellation
                raise

            except Exception as e:
                raise HTTPException(status_code=500, detail=f"Error when processing the invoice: {str(e)}")

    def encode_image(self, image_path: str) -> str:
        """
        Encode image file to base64 string with appropriate MIME type.

        Args:
            image_path (str): Path to image file

        Returns:
            str: Base64 encoded image with MIME type prefix
        """
        mime_type = self.get_mime_type(image_path)
        with open(image_path, "rb") as image_file:
            base64_image = base64.b64encode(image_file.read()).decode('utf-8')
        return f"data:{mime_type};base64,{base64_image}"

    def get_mime_type(self, image_path: str) -> str:
        """
        Determine MIME type based on image file extension.

        Args:
            image_path (str): Path to image file

        Returns:
            str: MIME type string
        """
        extension = image_path.split('.')[-1].lower()
        mime_types = {
            'png': 'image/png',
            'jpg': 'image/jpeg',
            'jpeg': 'image/jpeg',
            'tiff': 'image/tiff',
            'bmp': 'image/bmp',
        }
        return mime_types.get(extension, 'application/octet-stream')

    def select_invoice_model(self, invoice_type: str) -> type[BaseModel]:
        """
        Select appropriate Pydantic model based on invoice type.

        Args:
            invoice_type (str): Type of invoice

        Returns:
            type[BaseModel]: Pydantic model class
        """
        match invoice_type:
            case "Electricity Bill":
                InvoiceModel = InvoiceElectric
            case "Water Bill":
                InvoiceModel = InvoiceWater
            case "Gas Bill":
                InvoiceModel = InvoiceGas
            case _:
                InvoiceModel = InvoicePurchase

        return InvoiceModel

    def merge_related_invoices(self, page_data_list: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Merge related invoice data from multiple pages.

        This function:
        1. Merges pages without invoice numbers with the previous page that has one
        2. Merges pages with the same invoice number
        3. Adds delivery ticket page numbers to the invoice's delivery_ticket_pages list
        4. Handles problematic invoice numbers like 'PAGE X OF Y' or 'N/A'
        5. Tracks document_page_range for each invoice
        6. Aggregates token usage and cost information for each invoice

        Args:
            page_data_list (List[Dict[str, Any]]): List of structured data from each page

        Returns:
            List[Dict[str, Any]]: List of merged invoice data with aggregated token usage
        """
        if not page_data_list:
            return []

        merged_invoices = []
        current_invoice = None
        current_invoice_number = None
        current_page_range = []

        # Problematic invoice number patterns
        problematic_patterns = [
            'page', 'of', 'n/a', 'unknown', 'continuation',
            'not available', 'not applicable', 'none'
        ]

        for page_data in page_data_list:
            # Get invoice number and type from current page
            invoice_number = page_data.get('invoice_number', '')
            invoice_type = page_data.get('invoice_type', '')
            description = page_data.get('description', '')

            # Check if this is a continuation page based on description
            is_continuation = description.lower().strip() == 'continuation page'

            # Check if invoice number is problematic
            is_problematic = False
            if invoice_number:
                invoice_number_lower = invoice_number.lower().strip()
                is_problematic = any(pattern in invoice_number_lower for pattern in problematic_patterns)

            # Get the page number of the current page
            current_page = page_data.get('page_number', None)

            # Case 0: This page is a page-only document (delivery ticket, daily job tracking form, etc.)
            # Just include its page number in the previous invoice
            if invoice_type and self.is_page_only_document(invoice_type):
                if current_invoice:
                    # Get the page number of the page-only document
                    page_only_page = current_page

                    # Add the page-only document page to the current invoice's page_only_pages list
                    if 'page_only_pages' not in current_invoice:
                        current_invoice['page_only_pages'] = []

                    if page_only_page and page_only_page not in current_invoice['page_only_pages']:
                        current_invoice['page_only_pages'].append(page_only_page)

                    # Also maintain backward compatibility with delivery_ticket_pages
                    if invoice_type.lower() == 'delivery ticket':
                        if 'delivery_ticket_pages' not in current_invoice:
                            current_invoice['delivery_ticket_pages'] = []
                        if page_only_page and page_only_page not in current_invoice['delivery_ticket_pages']:
                            current_invoice['delivery_ticket_pages'].append(page_only_page)

                    # Update document_page_range to include this page-only document page
                    if 'document_page_range' in current_invoice and page_only_page:
                        current_page_range = current_invoice['document_page_range']
                        if page_only_page not in current_page_range:
                            current_page_range.append(page_only_page)
                            current_page_range.sort()  # Keep the range sorted
                            current_invoice['document_page_range'] = current_page_range
                else:
                    # If there's no current invoice, treat this as a standalone invoice
                    current_invoice = deepcopy(page_data)
                    current_invoice_number = invoice_number

                    # Initialize document_page_range
                    if current_page:
                        current_invoice['document_page_range'] = [current_page]

            # Case 1: This page has a valid invoice number (not empty and not problematic)
            elif invoice_number and invoice_number.strip() and not is_problematic:
                # Case 1.1: It's the same as the current invoice number - merge with current invoice
                if invoice_number == current_invoice_number:
                    # Update document_page_range before merging
                    if 'document_page_range' not in current_invoice:
                        current_invoice['document_page_range'] = []

                    if current_page and current_page not in current_invoice['document_page_range']:
                        current_invoice['document_page_range'].append(current_page)
                        current_invoice['document_page_range'].sort()  # Keep the range sorted

                    # Merge the invoice data
                    current_invoice = self.merge_invoice_data(current_invoice, page_data)
                # Case 1.2: It's a new invoice number - start a new invoice
                else:
                    # Save the previous invoice if it exists
                    if current_invoice:
                        # Ensure document_page_range is sorted
                        if 'document_page_range' in current_invoice and current_invoice['document_page_range']:
                            current_invoice['document_page_range'].sort()
                        merged_invoices.append(current_invoice)

                    # Start a new invoice
                    current_invoice = deepcopy(page_data)
                    current_invoice_number = invoice_number

                    # Token usage is handled in merge_invoice_data

                    # Initialize document_page_range
                    if current_page:
                        current_invoice['document_page_range'] = [current_page]

            # Case 2: This page has no invoice number, a problematic one, or is a continuation page
            # Merge with current invoice if it exists
            else:
                if current_invoice:
                    # Update document_page_range before merging
                    if 'document_page_range' not in current_invoice:
                        current_invoice['document_page_range'] = []

                    if current_page and current_page not in current_invoice['document_page_range']:
                        current_invoice['document_page_range'].append(current_page)
                        current_invoice['document_page_range'].sort()  # Keep the range sorted

                    # If it's a problematic invoice number, treat it as a continuation page
                    if is_problematic or is_continuation:
                        # Clear the problematic invoice number before merging
                        page_data_copy = deepcopy(page_data)
                        page_data_copy['invoice_number'] = ''
                        page_data_copy['description'] = 'Continuation page'
                        current_invoice = self.merge_invoice_data(current_invoice, page_data_copy)
                    else:
                        # Regular merge for empty invoice numbers
                        current_invoice = self.merge_invoice_data(current_invoice, page_data)
                else:
                    # If there's no current invoice, treat this as a standalone invoice
                    # But clear problematic invoice numbers
                    if is_problematic:
                        page_data_copy = deepcopy(page_data)
                        page_data_copy['invoice_number'] = ''
                        page_data_copy['description'] = 'Unknown invoice'
                        current_invoice = page_data_copy
                        current_invoice_number = ''
                    else:
                        current_invoice = deepcopy(page_data)
                        current_invoice_number = invoice_number

                    # Initialize document_page_range
                    if current_page:
                        current_invoice['document_page_range'] = [current_page]

        # Add the last invoice if it exists
        if current_invoice:
            # Ensure document_page_range is sorted
            if 'document_page_range' in current_invoice and current_invoice['document_page_range']:
                current_invoice['document_page_range'].sort()
            merged_invoices.append(current_invoice)

        # Final processing: ensure all invoices have a document_page_range
        for invoice in merged_invoices:
            # If document_page_range is missing but page_number exists, create it
            if 'document_page_range' not in invoice and 'page_number' in invoice:
                invoice['document_page_range'] = [invoice['page_number']]

            # If page_only_pages exists, ensure they're included in document_page_range
            if 'page_only_pages' in invoice and invoice['page_only_pages']:
                if 'document_page_range' not in invoice:
                    invoice['document_page_range'] = []

                # Add any missing page-only document pages to document_page_range
                for page in invoice['page_only_pages']:
                    if page not in invoice['document_page_range']:
                        invoice['document_page_range'].append(page)

                # Sort the page range
                invoice['document_page_range'].sort()

            # If delivery_ticket_pages exists, ensure they're included in document_page_range (backward compatibility)
            if 'delivery_ticket_pages' in invoice and invoice['delivery_ticket_pages']:
                if 'document_page_range' not in invoice:
                    invoice['document_page_range'] = []

                # Add any missing delivery ticket pages to document_page_range
                for page in invoice['delivery_ticket_pages']:
                    if page not in invoice['document_page_range']:
                        invoice['document_page_range'].append(page)

                # Sort the page range
                invoice['document_page_range'].sort()

            # Final post-processing: prioritize literal_total over final_total
            if 'total_amount' in invoice and isinstance(invoice['total_amount'], dict):
                total_amount = invoice['total_amount']
                literal_total = total_amount.get('literal_total')

                # If we have a valid literal_total, use it as the final_total
                if literal_total is not None and literal_total != 0.0:
                    total_amount['final_total'] = literal_total

        return merged_invoices

    def merge_invoice_data(self, base_invoice: Dict[str, Any], additional_data: Dict[str, Any], ignore_unique_fields: bool = False) -> Dict[str, Any]:
        """
        Merge additional invoice data into the base invoice.

        This function handles merging of:
        - List fields (concatenate)
        - Object fields (merge recursively)
        - Scalar fields (prioritize left to right - keep first non-empty value)
        - Token usage (sum values)

        Args:
            base_invoice (Dict[str, Any]): Base invoice data
            additional_data (Dict[str, Any]): Additional data to merge
            ignore_unique_fields (bool): If True, ignore unique identifier fields like invoice_number

        Returns:
            Dict[str, Any]: Merged invoice data
        """
        result = deepcopy(base_invoice)

        # Define unique fields that should be ignored when merging delivery tickets
        unique_fields = ['invoice_number', 'invoice_type', 'description'] if ignore_unique_fields else []

        # Special handling for token usage - aggregate values
        if 'token_usage' in additional_data:
            if 'token_usage' not in result:
                result['token_usage'] = deepcopy(additional_data['token_usage'])
            else:
                # Sum up token usage
                result['token_usage']['prompt_tokens'] += additional_data['token_usage']['prompt_tokens']
                result['token_usage']['completion_tokens'] += additional_data['token_usage']['completion_tokens']
                result['token_usage']['total_tokens'] += additional_data['token_usage']['total_tokens']
                result['token_usage']['cost'] += additional_data['token_usage']['cost']

        for key, value in additional_data.items():
            # Skip page_number field and unique fields if ignoring them
            if key == 'page_number' or key in unique_fields:
                continue

            # Special handling for document_page_range - merge and sort
            if key == 'document_page_range' and value:
                if key not in result:
                    result[key] = deepcopy(value)
                else:
                    # Merge page ranges without duplicates
                    for page in value:
                        if page not in result[key]:
                            result[key].append(page)
                    # Sort the merged page range
                    result[key].sort()
                continue

            # Special handling for page_only_pages - merge without duplicates
            if key == 'page_only_pages' and value:
                if key not in result:
                    result[key] = deepcopy(value)
                else:
                    # Merge page-only document pages without duplicates
                    for page in value:
                        if page not in result[key]:
                            result[key].append(page)
                    # Sort the merged list
                    result[key].sort()
                continue

            # Special handling for delivery_ticket_pages - merge without duplicates (backward compatibility)
            if key == 'delivery_ticket_pages' and value:
                if key not in result:
                    result[key] = deepcopy(value)
                else:
                    # Merge delivery ticket pages without duplicates
                    for page in value:
                        if page not in result[key]:
                            result[key].append(page)
                    # Sort the merged list
                    result[key].sort()
                continue

            # If the key doesn't exist in the result, add it
            if key not in result:
                result[key] = deepcopy(value)
                continue

            # Handle different types of values
            if isinstance(value, list) and isinstance(result[key], list):
                # Merge lists (e.g., invoice_items, products)
                # Remove duplicates when merging lists
                existing_items = set()
                if all(isinstance(item, dict) for item in result[key]):
                    # For lists of dictionaries, use a more complex deduplication
                    for item in result[key]:
                        # Create a hash of the item for deduplication
                        item_hash = tuple(sorted((k, str(v)) for k, v in item.items() if v))
                        existing_items.add(item_hash)

                    # Only add items that don't exist yet
                    for item in value:
                        item_hash = tuple(sorted((k, str(v)) for k, v in item.items() if v))
                        if item_hash not in existing_items:
                            result[key].append(deepcopy(item))
                            existing_items.add(item_hash)
                else:
                    # For simple lists, just extend and remove duplicates
                    result[key].extend(deepcopy(value))
                    # Remove duplicates while preserving order
                    seen = set()
                    result[key] = [x for x in result[key] if not (x in seen or seen.add(x))]
            elif isinstance(value, dict) and isinstance(result[key], dict):
                # Recursively merge dictionaries with special handling for total_amount
                if key == 'total_amount':
                    result[key] = self.merge_total_amount_fields(result[key], value)
                else:
                    result[key] = self.merge_dict_fields(result[key], value, ignore_unique_fields)
            else:
                # For scalar values, prioritize left to right (keep first non-empty value)
                # Only update if the base value is empty and the new value is not
                if not result[key] and value:
                    result[key] = deepcopy(value)

        return result

    def merge_total_amount_fields(self, base_total: Dict[str, Any], additional_total: Dict[str, Any]) -> Dict[str, Any]:
        """
        Merge total_amount fields with special logic for literal_total prioritization.

        This method prioritizes literal_total values when available and uses them to
        override final_total for more accurate total extraction.

        Args:
            base_total (Dict[str, Any]): Base total_amount data
            additional_total (Dict[str, Any]): Additional total_amount data to merge

        Returns:
            Dict[str, Any]: Merged total_amount data with prioritized literal_total
        """
        result = deepcopy(base_total)

        for key, value in additional_total.items():
            if key == 'literal_total':
                # Prioritize non-null literal_total values
                if value is not None and value != 0.0:
                    result[key] = value
                    # If we have a valid literal_total, use it as final_total too
                    result['final_total'] = value
                elif key not in result or result[key] is None:
                    result[key] = value
            elif key == 'final_total':
                # Only update final_total if we don't have a literal_total or if base is empty
                if result.get('literal_total') is None or result.get('literal_total') == 0.0:
                    if not result.get(key) and value:
                        result[key] = value
                # If we have literal_total, keep using that for final_total
                elif result.get('literal_total') is not None and result.get('literal_total') != 0.0:
                    result['final_total'] = result['literal_total']
            else:
                # For other fields, use standard merging logic
                if isinstance(value, (int, float)) and value != 0.0:
                    if key not in result or result[key] == 0.0:
                        result[key] = value
                    else:
                        # Sum numeric values for things like taxes, charges
                        if key in ['Taxes', 'shipping_charges', 'additional_charges', 'partial_total']:
                            result[key] = result.get(key, 0.0) + value
                        else:
                            # For other fields, keep first non-zero value
                            if result[key] == 0.0:
                                result[key] = value
                elif not result.get(key) and value:
                    result[key] = value

        return result

    def merge_dict_fields(self, base_dict: Dict[str, Any], additional_dict: Dict[str, Any], ignore_unique_fields: bool = False) -> Dict[str, Any]:
        """
        Recursively merge dictionary fields.

        Args:
            base_dict (Dict[str, Any]): Base dictionary
            additional_dict (Dict[str, Any]): Additional dictionary to merge
            ignore_unique_fields (bool): If True, ignore unique identifier fields

        Returns:
            Dict[str, Any]: Merged dictionary
        """
        result = deepcopy(base_dict)

        # Define unique fields that should be ignored when merging delivery tickets
        unique_fields = ['invoice_number', 'invoice_type', 'description'] if ignore_unique_fields else []

        for key, value in additional_dict.items():
            # Skip unique fields if ignoring them
            if key in unique_fields:
                continue

            # Special handling for document_page_range - merge and sort
            if key == 'document_page_range' and value:
                if key not in result:
                    result[key] = deepcopy(value)
                else:
                    # Merge page ranges without duplicates
                    for page in value:
                        if page not in result[key]:
                            result[key].append(page)
                    # Sort the merged page range
                    result[key].sort()
                continue

            # Special handling for page_only_pages - merge without duplicates
            if key == 'page_only_pages' and value:
                if key not in result:
                    result[key] = deepcopy(value)
                else:
                    # Merge page-only document pages without duplicates
                    for page in value:
                        if page not in result[key]:
                            result[key].append(page)
                    # Sort the merged list
                    result[key].sort()
                continue

            # Special handling for delivery_ticket_pages - merge without duplicates (backward compatibility)
            if key == 'delivery_ticket_pages' and value:
                if key not in result:
                    result[key] = deepcopy(value)
                else:
                    # Merge delivery ticket pages without duplicates
                    for page in value:
                        if page not in result[key]:
                            result[key].append(page)
                    # Sort the merged list
                    result[key].sort()
                continue

            # If the key doesn't exist in the result, add it
            if key not in result:
                result[key] = deepcopy(value)
                continue

            # Handle different types of values
            if isinstance(value, list) and isinstance(result[key], list):
                # Merge lists with deduplication
                if all(isinstance(item, dict) for item in result[key]):
                    # For lists of dictionaries, use a more complex deduplication
                    existing_items = set()
                    for item in result[key]:
                        # Create a hash of the item for deduplication
                        item_hash = tuple(sorted((k, str(v)) for k, v in item.items() if v))
                        existing_items.add(item_hash)

                    # Only add items that don't exist yet
                    for item in value:
                        item_hash = tuple(sorted((k, str(v)) for k, v in item.items() if v))
                        if item_hash not in existing_items:
                            result[key].append(deepcopy(item))
                            existing_items.add(item_hash)
                else:
                    # For simple lists, just extend and remove duplicates
                    result[key].extend(deepcopy(value))
                    # Remove duplicates while preserving order
                    seen = set()
                    result[key] = [x for x in result[key] if not (x in seen or seen.add(x))]
            elif isinstance(value, dict) and isinstance(result[key], dict):
                # Recursively merge dictionaries
                result[key] = self.merge_dict_fields(result[key], value, ignore_unique_fields)
            else:
                # For scalar values, prioritize left to right (keep first non-empty value)
                if not result[key] and value:
                    result[key] = deepcopy(value)

        return result


    def split_invoices(self, text: str) -> list:
            """
            Split and process invoice text into separate invoices, merging delivery tickets with their corresponding invoices.

            This method performs two main operations:
            1. Splits the input text into individual invoices based on invoice numbers
            2. Merges any delivery tickets with their preceding invoices. Multiple delivery tickets
            for the same invoice will all be merged with that invoice.

            Args:
                text (str): Raw text containing one or multiple invoices

            Returns:
                list: List of processed invoice texts, with delivery tickets merged into their corresponding invoices

            Example:
                >>> text = '''
                Invoice Number: 001
                Content...
                Invoice Number: 002
                Invoice Type: Delivery ticket
                Content...
                Invoice Number: 003
                Invoice Type: Delivery ticket
                Content...
                Invoice Number: 004
                Content...
                '''
                >>> processor = InvoiceProcessor()
                >>> invoices = processor.split_invoices(text)
                >>> # Returns [Invoice001 + DeliveryTicket002 + DeliveryTicket003, Invoice004]
            """
            lines = text.split('\n')
            invoices = []
            current_invoice_text = ''
            current_invoice_number = None

            for line in lines:
                line = line.strip()
                # Buscar 'Invoice Number' con o sin asteriscos
                match = re.match(r'-\s*\**Invoice Number:?\**\s*(.*)', line, re.IGNORECASE)
                if match:
                    invoice_number = match.group(1).strip()
                    if invoice_number and invoice_number != current_invoice_number:
                        if current_invoice_number is not None:
                            # Si ya hemos iniciado una factura previa, agregamos el texto acumulado a la lista
                            if current_invoice_text.strip():
                                invoices.append(current_invoice_text.strip())
                            # Iniciamos una nueva factura
                            current_invoice_text = ''
                        current_invoice_number = invoice_number
                    # Añadimos la línea actual al texto de la factura
                    current_invoice_text += '\n' + line
                else:
                    current_invoice_text += '\n' + line

            if current_invoice_text.strip():
                invoices.append(current_invoice_text.strip())

            # Process page-only documents - add page numbers to preceding invoices
            merged_invoices = []
            i = 0
            while i < len(invoices):
                current_invoice = invoices[i]
                # Check if next invoice is a page-only document
                if i + 1 < len(invoices):
                    next_invoice = invoices[i + 1]

                    # Check for any page-only document type
                    is_page_only = False
                    page_only_type = None
                    for doc_type in self.page_only_document_types:
                        if f"Invoice Type: {doc_type.title()}" in next_invoice:
                            is_page_only = True
                            page_only_type = doc_type.title()
                            break

                    if is_page_only:
                        # Extract page number from the page-only document if available
                        page_match = re.search(r"<<PageNumber:\s*(\d+)", next_invoice)
                        page_number = int(page_match.group(1)) if page_match else None

                        # Add a note about the page-only document page
                        if page_number:
                            page_only_note = f"\n\n{page_only_type} on page {page_number}"
                            current_invoice += page_only_note

                        merged_invoices.append(current_invoice)
                        i += 2
                    else:
                        merged_invoices.append(current_invoice)
                        i += 1
                else:
                    merged_invoices.append(current_invoice)
                    i += 1

            return merged_invoices

    def extract_invoice_type(self, invoice_text: str) -> str:
        """
        Extract invoice type from processed text.

        Args:
            invoice_text (str): Processed invoice text

        Returns:
            str: Invoice type or "Purchase Invoice" as default
        """
        pattern = re.compile(r"Invoice Type:\s*(.*)")
        match = pattern.search(invoice_text)
        invoice_type = match.group(1).strip() if match else "Purchase Invoice"
        return invoice_type

    async def get_structured_data(self, invoice_text: str, InvoiceModel: type[BaseModel]):
        """
        Extract structured data from invoice text using language model.

        Implements retry logic for rate limit handling.

        Args:
            invoice_text (str): Processed invoice text
            InvoiceModel (type[BaseModel]): Pydantic model to structure the data

        Returns:
            dict: {
                "response": dict,  # Structured invoice data
                "token_usage": {   # Token usage information
                    "prompt_tokens": int,
                    "completion_tokens": int,
                    "total_tokens": int,
                    "cost": float
                }
            }

        Raises:
            ValidationError: If data doesn't match model
            HTTPException: On rate limit or processing errors
        """
        prompt = """
        You will receive a text containing information about an invoice.
        Your goal is to extract structured information from the provided invoice.
        Focus on identifying key details such as 'invoice_number', 'issued_by', 'billing_date',
        'due_date', 'total_amount_due', 'items_list', and any specific charges relevant to the invoice type.
        Ensure accuracy and completeness in your extraction.

        take into account the structured model you are provided
        """

        data = f"Here is the invoice text: {invoice_text}"

        try:
            response = await self.langchain_client.get_structured_data(InvoiceModel, data, prompt)
            return response  # Return the full response including token_usage
        except Exception as e:
            print(e)
            raise HTTPException(status_code=429, detail=f"Error processing invoice: {e}")
