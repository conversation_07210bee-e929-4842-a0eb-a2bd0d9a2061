# LumusAI

LumusAI is an intelligent document processing service that leverages AI to extract, analyze, and structure information from various document types. It provides specialized processors for CVs, legal documents, and invoices.

## Table of Contents

| Section | Description | Subsections |
|---------|-------------|-------------|
| [🚀 Features](#features) | Core functionalities | • CV Processing (SmartHR)<br>• Legal Document Processing (Papirus)<br>• Invoice Processing (Facturius) |
| [🏁 Getting Started](#getting-started) | Setup and installation | • Prerequisites<br>• Installation Steps<br>• Quick Setup Guide |
| [📡 API Documentation](#api-documentation) | Endpoint references | • Document Processing API<br>• Health Monitoring<br>• Error Handling<br>• Authentication |
| [🏗️ Project Structure](#project-structure) | Codebase organization | • Architecture Overview<br>• Key Components<br>• Design Patterns |
| [⚙️ Configuration](#configuration) | System settings | • Environment Variables<br>• Security Settings |
| [👨‍💻 Development](#development) | Developer guidelines | • Coding Standards<br>• Testing Procedures<br>• Best Practices |
| [🐳 Docker Support](#docker-support) | Containerization | • Container Setup<br>• Deployment Guide<br>• Production Config |
| [👏 Acknowledgments](#acknowledgments) | Credits and thanks | • Contributors<br>• Third-party Tools<br>• Open Source Libraries |

## Features

### CV Processing (SmartHR)
- Comprehensive information extraction from resumes/CVs
- Support for PDF and DOCX formats
- Multi-language support with automatic translation to English
- Structured data extraction including:
  - Personal and contact details
  - Professional experience
  - Education history
  - Skills and certifications
  - Technical skill duration calculation
- Automatic skill aggregation across experiences
- Role suitability analysis

### Legal Document Processing (Papirus)
- Processing of legal documents including:
  - Court decisions (Tutela Fallos)
  - Legal correspondence
  - Legal notices
  - Court orders (Desacatos)
- Automated text extraction and classification
- Structured data output in standardized formats
- Legal entity recognition
- Document type classification

### Invoice Processing (Facturius)
- Automated invoice data extraction
- Support for multiple formats (PDF, Excel, Images)
- Intelligent field recognition and categorization
- Data validation and standardization
- Tax calculation verification
- Multi-currency support
- Vendor recognition and classification

## Getting Started

### Prerequisites
- Python 3.12.7
- Docker and Docker Compose
- OpenAI API key
- LangChain compatible environment

### Installation

1. Clone the repository:
```bash
git clone https://<EMAIL>/arroyoconsultingco/Lumus/_git/LumusAI
cd LumusAI
```

2. Set up environment variables:
```bash
cp .env.example .env
# Edit .env with your configuration
```

3. Run with Docker:
```bash
docker-compose up --build
```

Or run locally:
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt
uvicorn main:app --reload
```

### Troubleshooting
Common issues and solutions:
- OpenAI API errors: Verify API key and quota
- Memory issues: Increase Docker container memory limit

## API Documentation

### Available Endpoints

#### Health Check
```http
GET /health
```
Returns service health status and system metrics, including information about currently processing and waiting tasks. The number of concurrent tasks is configurable via the `MAX_CONCURRENT_TASKS` environment variable.

Response example:
```json
{
    "status": "ok",
    "message": "Service is running",
    "system_metrics": {
        "cpu_usage_percent": 45.2,
        "memory_usage_percent": 62.8
    },
    "tasks": {
        "processing_count": 2,
        "processing_details": [...],
        "waiting_count": 1
    }
}
```

#### Document Processing

##### CV Processing
```http
POST /process
Content-Type: multipart/form-data

Parameters:
- action: "cv" (required)
- file: PDF/DOCX file OR
- data: URL to the document
```

Example curl:
```bash
curl -X POST "http://localhost:8004/apis/lumusai/process" \
     -F "action=cv" \
     -F "file=@path/to/resume.pdf"
```

Example curl with URL:
```bash
curl -X POST "http://localhost:8004/apis/lumusai/process" \
     -F "action=cv" \
     -F "data=https://example.com/path/to/resume.pdf"
```

##### Legal Document Processing
```http
POST /process
Content-Type: multipart/form-data

Parameters:
- action: One of ["tutela_fallo", "tutela_desacato", "tutela_contestacion"] (required)
- file: PDF file OR
- data: URL to the document
```

Example curl:
```bash
curl -X POST "http://localhost:8004/apis/lumusai/process" \
     -F "action=tutela_fallo" \
     -F "file=@path/to/legal_doc.pdf"
```
##### Legal Document Processing (email)
```http
POST /process
Content-Type: multipart/form-data

Parameters:
- action: "tutela_correo" (required)
- file: txt file OR
- data: URL to the document
```

Example curl:
```bash
curl -X POST "http://localhost:8004/apis/lumusai/process" \
     -F "action=tutela_correo" \
     -F "file=@path/to/email.txt"
```

##### Invoice Processing
```http
POST /process
Content-Type: multipart/form-data

Parameters:
- action: "invoice" (required)
- file: PDF/Excel/Image file
- data: URL to the document
```

Example curl:
```bash
curl -X POST "http://localhost:8004/apis/lumusai/process" \
     -H "Authorization: Bearer your_api_key" \
     -F "action=invoice" \
     -F "file=@path/to/invoice.pdf"
```

### Error Response
```json
{
    "status": "error",
    "error": {
        "code": "ERROR_CODE",
        "message": "Detailed error message"
    }
}
```

### Common Error Codes
- `400`: Bad Request (Invalid parameters or file format)
- `401`: Unauthorized (Invalid or missing API key)
- `413`: Payload Too Large (File size exceeds limit)
- `415`: Unsupported Media Type
- `429`: Too Many Requests (Rate limit exceeded)
- `500`: Internal Server Error

## Project Structure

```
LumusAI/
├── domain/
│   └── models/
│       └── smarthr/
│           └── cv_model.py
│       └── papirus/
│           └── tutela_model.py
│       └── facturius/
│           └── factura_model.py
├── services/
│   └── processors/
│       └── smarthr/
│           └── cv_processor.py
│       └── papirus/
│           ├── tutela_fallo_processor.py
│           ├── tutela_correo_processor.py
│           ├── tutela_desacato_processor.py
│           └── tutela_contestacion_processor.py
│       └── facturius/
│           └── invoice_processor.py
├── utils/
│   ├── helpers.py
│   └── langchain_client.py
├── main.py
└── Dockerfile
```

### Key Components
- `domain/models/`: Pydantic models for response structure
- `services/processors/`: Document processing logic
- `utils/`: Shared utilities and helpers

## Configuration

### Environment Variables
```env
# Required
API_KEY=
API_VERSION=
AZURE_ENDPOINT=
MODEL=

# Optional
MAX_CONCURRENT_TASKS=4  # Number of documents that can be processed simultaneously (default: 4)
```
## Development

### Commit and branch Guidelines
We use conventional commits and branch naming:
- `feat:` New features
- `fix:` Bug fixes
- `docs:` Documentation or comment improvements
- `refactor:` Code changes that don’t alter functionality, just improve structure
- `test:` Adding or modifying tests
- `perf:` Performance improvements
- `revert:` Reverting changes
- `spike:` Experimental features or changes

### Running Tests
```bash
# Run all tests
pytest

# Run specific test category
pytest tests/test_cv_processor.py
pytest tests/test_legal_processor.py
pytest tests/test_invoice_processor.py

# Run with coverage
pytest --cov=.
```

### Code Style Guidelines

We follow strict coding standards to maintain code quality and consistency:

#### Python Style
- Follow PEP 8 style guide
- Maximum line length: 88 characters
- Use meaningful variable and function names
- Include docstrings for all public modules, functions, classes, and methods
- Type hints are mandatory for function parameters and return values

#### Naming Conventions
- Classes: PascalCase (`class UserAuthentication:`)
- Functions/Variables: snake_case (`def process_document():`, `user_input = []`)
- Constants: UPPERCASE with underscores (`MAX_UPLOAD_SIZE = 10`)
- Private methods/variables: leading underscore (`_internal_method()`)
- Protected methods/variables: double leading underscore (`__very_private()`)

#### Code Organization
- One class per file
- Related functionality grouped in modules
- Maximum function length: 50 lines
- Maximum class length: 300 lines
- Maximum module length: 500 lines

#### Documentation
- All public APIs must have docstrings
- Use Google-style docstring format:
```python
def function_name(param1: str, param2: int) -> bool:
    """Short description of function.

    Longer description if needed.

    Args:
        param1: Description of param1
        param2: Description of param2

    Returns:
        Description of return value

    Raises:
        ValueError: Description of when this error occurs
    """
```

#### Comments
- Comments should explain WHY, not WHAT
- Keep comments current with code changes
- Avoid obvious comments
- Use TODO comments for temporary code or future improvements

#### Imports
- Group imports in the following order:
  1. Standard library imports
  2. Third-party imports
  3. Local application imports
- One import per line
- Avoid wildcard imports (`from module import *`)

#### Error Handling
- Use explicit exception handling
- Catch specific exceptions rather than using bare except
- Include error messages that are helpful for debugging

#### Best Practices
- DRY (Don't Repeat Yourself)
- SOLID principles
- Composition over inheritance
- Early returns over nested conditionals
- Avoid global variables
- Use f-strings for string formatting


## Docker Support

The project includes Docker support for easy deployment:

### Features
- Multi-stage build for optimized image size
- Alpine-based Python image

### Commands
```bash
# Build and run
docker-compose up --build

# Run in background
docker-compose up -d

# Stop services
docker-compose down
```

## Acknowledgments

- OpenAI for GPT models
- LangChain for AI integration
- FastAPI framework
- Docker for containerization
- Python community

## Support

For support, please open an issue <NAME_EMAIL>
