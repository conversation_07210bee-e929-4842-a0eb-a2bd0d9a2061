from pydantic import BaseModel, Field, field_validator
from typing import List, Optional
from datetime import date

# Base Models
class Address(BaseModel):
    name: str = Field(..., description="Name of the person or organization")
    address_line: str = Field(..., description="Local address information like street, building number, PO Box, or apartment")
    city: str = Field(..., description="City")
    state_province_code: str = Field(..., description="State or province code")
    postal_code: str = Field(..., description="Postal code")
    phone_number: Optional[str] = Field(None, description="Phone number")
    tax_identification_number: Optional[str] = Field(..., description="Tax Identification Number of the vendor (NIT, RFC, RUC, CUIT, RUT, or any equivalent depending on the country). Always extract the code as it appears.")

class BillingDateInfo(BaseModel):
    due_date: str = Field("", description="Payment due date, change the format to yyyy-mm-dd format")
    due_date_raw: str = Field("", description="Payment due date with the original date format")
    issue_date: str = Field("", description="Invoice issue date, change the format to yyyy-mm-dd")
    issue_date_raw: str = Field("", description="Invoice issue date with the original date format")

class TotalBill(BaseModel):
    partial_total: float = Field(..., description="Pre-tax subtotal amount before taxes and additional charges. Look for 'Pre-Tax Total', 'Subtotal', or similar labels (if it is null put 0.0 instead)")
    discount_amount: Optional[float] = Field(0.0, description="Applied discount amount. Look for 'Discount', 'Rebate', or similar labels (if it is null put 0.0 instead)")
    Taxes: float = Field(0.0, description="Total tax amount. Sum all tax line items including sales tax, state tax, city tax, etc. Look for individual tax amounts and add them together (if it is null put 0.0 instead)")
    shipping_charges: Optional[float] = Field(0.0, description="Shipping and freight charges. Look for 'Freight', 'Shipping', 'Delivery', 'Crate Charge', or similar labels (if it is null put 0.0 instead)")
    additional_charges: Optional[float] = Field(0.0, description="Additional charges like service fees, handling fees, or surcharges. Look for 'Materials Inflation Surcharge' or similar additional fees (if it is null put 0.0 instead)")
    literal_total: Optional[float] = Field(None, description="ONLY extract this if there is a literal total amount clearly labeled as 'Amount Due', 'Total', 'Total Due', 'Balance Due', 'Invoice Total', or similar explicit total labels. Do NOT calculate or sum - only extract the exact value next to these labels (if not found, leave as null)")
    final_total: float = Field(..., description="Final total amount due. Look for 'Amount Due', 'Total Due', 'Final Total', or the largest amount on the invoice (if it is null put 0.0 instead)")

class InvoiceBase(BaseModel):
    invoice_number: str = Field(..., description="The actual invoice number. DO NOT use page numbers like 'PAGE 2 OF 4' or placeholders like 'N/A'. If you can't find a clear invoice number on this page but found one on a previous page, leave this field empty. Only extract actual invoice identifiers.")
    invoice_type: str = Field(..., description="Invoice type, Choose one of: 'Delivery ticket', 'Daily job tracking form', 'Credit Note', 'Purchase', 'Other'")
    description: str = Field(..., description="Just include the invoice number value, dont add anything else aside from that. for example: instead of 'Invoice number: 1223' just write '1223'. If no invoice number is found, write 'Continuation page'")
    vendor: Address = Field(..., description="Information about the invoice issuer")
    billing_address: Address = Field(..., description="Recipient's billing address")
    billing_date_info: BillingDateInfo = Field(..., description="Billing and payment dates")
    document_page_range: List[int] = Field(..., description="List of page numbers extracted from the document in the format <<PageNumber: >>")

# Specific Models

class ProductItem(BaseModel):
    product_description: str = Field(..., description="Description of the product or service")
    quantity: int = Field(..., description="Number of units")
    unit_price: float = Field(..., description="Price per unit (if it is null put 0.0 instead)")
    total_price: float = Field(..., description="Total price for the product or service (if it is null put 0.0 instead)")

class ConsumptionDetail(BaseModel):
    period_start: str = Field(..., description="Consumption period start date in MM/DD/YYYY")
    period_end: str = Field(..., description="Consumption period end date in MM/DD/YYYY")
    consumption_units: float = Field(..., description="Units consumed during the period (if it is null put 0.0 instead)")
    unit_of_measurement: str = Field(..., description="Unit of measurement (e.g., kWh, m³)")
    rate_per_unit: float = Field(..., description="Rate per unit consumed (if it is null put 0.0 instead)")
    total_consumption_charge: float = Field(..., description="Total charge for consumption (if it is null put 0.0 instead)")

class AccountSummary(BaseModel):
    previous_balance: float = Field(..., description="Previous account balance (if it is null put 0.0 instead)")
    payments: float = Field(..., description="Payments made (if it is null put 0.0 instead)")
    balance_forward: float = Field(..., description="Carried forward balance (if it is null put 0.0 instead)")
    adjustments: Optional[float] = Field(0.0, description="Adjustments made to the account (if it is null put 0.0 instead)")
    current_billing_charges: float = Field(..., description="Current billing charges (if it is null put 0.0 instead)")
    account_balance: float = Field(..., description="Current account balance (if it is null put 0.0 instead)")

# Purchase Invoice

class InvoicePurchase(InvoiceBase):
    total_amount: TotalBill = Field(..., description="Total billing details")
    invoice_items: List[ProductItem] = Field(..., description="List of invoiced products or services")

# Electricity Invoice

class InvoiceElectric(InvoiceBase):
    total_amount: TotalBill = Field(..., description="Total billing details")
    consumption_details: ConsumptionDetail = Field(..., description="Electricity consumption details")
    account_summary: AccountSummary = Field(..., description="Account summary")
    products: List[ProductItem] = Field(..., description="List of invoiced electricity services")

# Water Invoice

class InvoiceWater(InvoiceBase):
    total_amount: TotalBill = Field(..., description="Total billing details")
    consumption_details: ConsumptionDetail = Field(..., description="Water consumption details")
    account_summary: AccountSummary = Field(..., description="Account summary")

# Gas Invoice

class InvoiceGas(InvoiceBase):
    total_amount: TotalBill = Field(..., description="Total billing details")
    consumption_details: ConsumptionDetail = Field(..., description="Gas consumption details")
    account_summary: AccountSummary = Field(..., description="Account summary")