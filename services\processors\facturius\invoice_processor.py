from services.processors.base_processor import DocumentProcessor
from utils.openai_client import OpenAIClient
from utils.langchain_client import LangChainClient
from fastapi import UploadFile, HTTPException
from typing import Optional
from pydantic import BaseModel, ValidationError
import os
import tempfile
import fitz
import time
import json
import base64
import re
import requests
from domain.models.facturius.invoice_model import (
    InvoicePurchase,
    InvoiceElectric,
    InvoiceWater,
    InvoiceGas,
)
import openai
import subprocess
from utils.helpers import convert_excel_to_csv, convert_xls_to_csv, format_float_values
import asyncio
import gc


prompt_text = """
        Extract all relevant information from the provided CSV data and present it in a standardized format, without adding any notes or additional information.
        If the information is missing, do NOT make up new info; leave it blank.

        Invoice Details:
        - Invoice Number: [Value]
        - Invoice Type: [Choose one of: 'Delivery ticket', 'Daily job tracking form', 'Credit Note', 'Purchase', 'Other']
        - Billing_Date: [Value] (if there is no due date leave it null, keep the original date format)
        - Due Date: [Value] (if there is no due date leave it empty)
        - Account Number: [Value]
        - Total Amount Due: [Value]
        - Paid Amount: [Value]
        - Outstanding Balance: [Value]
        - Billing name: [Value]

        Vendor/Issued By:
        - name: [Value]
        - Address: [Value]
        - Telephone: [Value]
        - Fax: [Value]

        Total details:
        - Subtotal: [Value] (Pre-tax total before taxes and additional charges)
        - Taxes: [Value] (Sum ALL individual tax amounts - city, state, sales tax, etc.)
        - Discount: [Value]
        - Shipping Charges: [Value] (Freight, delivery, crate charges)
        - Additional Charges: [Value] (Materials inflation surcharge, service fees)
        - Total amount: [Value] (Final amount due - the amount customer must pay)

        Additional Information (only if there is relevant information list it in here, naming each field acordingly):
        - [Label]: [Value]

        (Continue listing fields as needed based on the information presented on the invoice.)

        Items List:
        1. Item: [Description]
        - Quantity: [Value]
        - Unit Price: [Value]
        - Total Price: [Value]

        (Continue numbering for each item.)

        Instructions:
        + All dates must be in MM/DD/YYYY format
        + Identify the type of bill or invoice from the list and extract all key information, including the issuer, dates, and total amount due.
        + Extract additional relevant fields such as payment terms, service dates, or any breakdown of charges that are included in the invoice.
        + Include every item from the invoice's 'Items List' without omission, even if items appear similar.
        + Extract accurately all specified fields, paying special attention to the Account Summary in service invoices.
        + Do not add any notes or additional comments; only include the information extracted from the invoice.
        + Present all information clearly and in the order shown above.
        + If there is a field missing from the invoice leave it blank

        """

class InvoiceProcessor(DocumentProcessor):
    """
    Processor for handling and extracting information from various types of invoices.

    Supports multiple file formats including PDF, images (PNG, JPG, JPEG, TIFF, BMP),
    and spreadsheets (XLS, XLSX, CSV). Processes invoices to extract structured data
    using OCR and language models.

    Attributes:
        openai_client (OpenAIClient): Client for OpenAI API interactions
        langchain_client (LangChainClient): Client for LangChain operations
    """
    def __init__(self, openai_client: OpenAIClient, langchain_client: LangChainClient):
        self.openai_client = openai_client
        self.langchain_client = langchain_client

    async def process(self, file: Optional[UploadFile], data: Optional[str] = None) -> dict:
        """
        Main entry point for invoice processing.

        Args:
            file (Optional[UploadFile]): File to process
            data (Optional[str]): URL to download file from

        Returns:
            dict: Processed invoice data

        Raises:
            HTTPException: If neither file nor URL is provided
        """
        if not file and not data:
            raise HTTPException(status_code=400, detail="The Invoice action requires a file or URL")
        result = await self.process_invoice(file, data)
        return result

    async def process_invoice(self, file: UploadFile, data: Optional[str] = None) -> dict:
        """
        Process invoice file or URL and extract structured data.

        Handles multiple file formats:
        - PDF: Converts pages to images and processes with OCR
        - Images: Direct OCR processing
        - Spreadsheets: Converts to CSV and processes content

        Args:
            file (UploadFile): File to process
            data (Optional[str]): URL to download file from

        Returns:
            dict: List of structured invoice data

        Raises:
            HTTPException: For various processing errors
        """
        with tempfile.TemporaryDirectory() as temp_dir:
            try:
                if data:
                    # Descargar el archivo desde la URL proporcionada en 'data'
                    url = data.strip()
                    response = requests.get(url)
                    if response.status_code != 200:
                        raise HTTPException(status_code=400, detail="It could not download the file from the URL")
                    # Obtener el nombre del archivo desde la URL
                    filename = url.split("/")[-1].split("?")[0]
                    file_extension = filename.split(".")[-1].lower()
                    file_content = response.content
                else:
                    # Usar el archivo subido
                    filename = file.filename
                    file_extension = filename.split(".")[-1].lower()
                    file_content = await file.read()

                image_extensions = ['png', 'jpg', 'jpeg', 'tiff', 'bmp']

                invoices_text_list = []  # Initialize the list to store results

                if file_extension == 'pdf':
                    # Guardar el PDF en el directorio temporal
                    pdf_path = os.path.join(temp_dir, filename)
                    with open(pdf_path, 'wb') as f:
                        f.write(file_content)

                    invoice_tot_text = ""

                    # Abrir el PDF y extraer imágenes de las páginas
                    with fitz.open(pdf_path) as doc:
                        page_num = 0
                        for page in doc:
                            page_num += 1
                            # Renderizar la página a una imagen
                            pix = page.get_pixmap(dpi=300)
                            # Guardar la imagen en el directorio temporal
                            image_filename = f"page-{page.number}.png"
                            image_path = os.path.join(temp_dir, image_filename)
                            pix.save(image_path)
                            # Procesar la imagen
                            content =  await self.sheet_reader(image_path)
                            invoice_tot_text += content
                            invoice_tot_text += f"<<PageNumber: {page_num}>>"
                            # Eliminar la imagen después de procesarla
                            os.remove(image_path)
                            gc.collect()
                    # Split the accumulated text into invoices
                    os.remove(pdf_path)
                    invoices_text_list = self.split_invoices(invoice_tot_text)

                elif file_extension in image_extensions:
                    # Guardar la imagen en el directorio temporal
                    image_path = os.path.join(temp_dir, filename)
                    with open(image_path, 'wb') as f:
                        f.write(file_content)
                    # Procesar la imagen directamente
                    invoice_tot_text = await self.sheet_reader(image_path)
                    os.remove(image_path)
                    # Split the accumulated text into invoices
                    invoices_text_list = self.split_invoices(invoice_tot_text)

                elif file_extension in ['xls', 'xlsx', 'csv']:
                    # Save the file in the temporary directory`
                    csv_tot_text = ''
                    sheet_path = os.path.join(temp_dir, filename)
                    with open(sheet_path, 'wb') as f:
                        f.write(file_content)
                    try:
                        if file_extension == 'csv':
                            with open(sheet_path, "r", encoding="utf-8") as file:
                                csv_content = file.read()

                            os.remove(sheet_path)
                            prompt_with_csv = f"{prompt_text}\nCSV Content:\n{csv_content}"

                            max_retries = 3
                            for attempt in range(max_retries):
                                try:
                                    response = await self.langchain_client.simple_invoke(prompt_with_csv)
                                    csv_text_content = response
                                    # Append the result to invoices_text_list
                                    invoices_text_list.append(csv_text_content)
                                    break
                                except Exception as e:
                                    if attempt < max_retries - 1:
                                        print("Rate limit reached, waiting 40 seconds before retrying...")
                                        await asyncio.sleep(40)
                                    else:
                                        raise HTTPException(status_code=429, detail="Rate limit reached, please try again later.")

                        elif file_extension == 'xls':
                            # Use convert_xls_to_csv
                            csv_contents = convert_xls_to_csv(sheet_path)
                            os.remove(sheet_path)
                        elif file_extension == 'xlsx':
                            # Use convert_excel_to_csv
                            csv_contents = convert_excel_to_csv(sheet_path)
                            os.remove(sheet_path)
                        else:
                            raise HTTPException(status_code=400, detail="Unsupported file format.")

                        if file_extension in ['xls', 'xlsx']:
                            sheet_num = 0
                            for csv_content in csv_contents:
                                # Combine the prompt with the CSV content
                                prompt_with_csv = f"{prompt_text}\nCSV Content:\n{csv_content}"
                                # Call OpenAI to process the CSV content
                                max_retries = 3
                                for attempt in range(max_retries):
                                    try:
                                        sheet_num += 1
                                        response = await self.langchain_client.simple_invoke(prompt_with_csv)
                                        csv_text_content = response
                                        # Append the result to invoices_text_list
                                        csv_tot_text += csv_text_content
                                        csv_tot_text += f"<<PageNumber: {sheet_num}>>"
                                        break
                                    except Exception as e:
                                        if attempt < max_retries - 1:
                                            print("Rate limit reached, waiting 40 seconds before retrying...")
                                            await asyncio.sleep(40)
                                        else:
                                            raise HTTPException(status_code=429, detail="Rate limit reached, please try again later.")
                            invoices_text_list = self.split_invoices(csv_tot_text)

                    except Exception as e:
                        raise HTTPException(status_code=500, detail=f"Error processing CSV or Excel file: {str(e)}")

                else:
                    raise HTTPException(status_code=400, detail="the file must be a PDF, image, or a CSV/Excel.")

                # Now, process each invoice_text in invoices_text_list

                all_invoice_data = []

                for invoice_text in invoices_text_list:
                    # Determinar el tipo de factura a partir del texto obtenido
                    invoice_type = self.extract_invoice_type(invoice_text)

                    # Seleccionar el modelo adecuado según el tipo de factura
                    InvoiceModel = self.select_invoice_model(invoice_type)

                    # Realizar la segunda llamada a OpenAI para obtener datos estructurados
                    invoice_data = await self.get_structured_data(invoice_text, InvoiceModel)

                    # Añadir los datos de la factura a la lista
                    all_invoice_data.append(invoice_data)

                # Devolver la lista de datos estructurados
                return all_invoice_data

            except Exception as e:
                raise HTTPException(status_code=500, detail=f"Error when processing the invoice: {str(e)}")


    def encode_image(self, image_path: str) -> str:
        """
        Encode image file to base64 string with appropriate MIME type.

        Args:
            image_path (str): Path to image file

        Returns:
            str: Base64 encoded image with MIME type prefix
        """
        mime_type = self.get_mime_type(image_path)
        with open(image_path, "rb") as image_file:
            base64_image = base64.b64encode(image_file.read()).decode('utf-8')
        return f"data:{mime_type};base64,{base64_image}"


    def get_mime_type(self, image_path: str) -> str:
        """
        Determine MIME type based on image file extension.

        Args:
            image_path (str): Path to image file

        Returns:
            str: MIME type string
        """
        extension = image_path.split('.')[-1].lower()
        mime_types = {
            'png': 'image/png',
            'jpg': 'image/jpeg',
            'jpeg': 'image/jpeg',
            'tiff': 'image/tiff',
            'bmp': 'image/bmp',
        }
        return mime_types.get(extension, 'application/octet-stream')


    async def sheet_reader(self, image_path: str) -> str:
        """
        Extract text from image using OCR via LangChain.

        Args:
            image_path (str): Path to image file

        Returns:
            str: Extracted text from image

        Raises:
            HTTPException: Passed through from LangChainClient
        """
        # Encode the image to base64
        base64_image_data = self.encode_image(image_path)

        # Define OCR prompt
        ocr_prompt = """Extract all text from this image accurately. Preserve the original formatting as much as possible.
        Include all numbers, dates, and special characters. Maintain the structure of tables if present."""

        # Call the langchain_client's extract_data method
        # The helper function handles all validation, retries, and error cases
        response = await self.langchain_client.extract_data(ocr_prompt, base64_image_data)

        # Return the response content
        return response['response']

    def split_invoices(self, text: str) -> list:
        """
        Split and process invoice text into separate invoices, merging delivery tickets with their corresponding invoices.

        This method performs two main operations:
        1. Splits the input text into individual invoices based on invoice numbers
        2. Merges any delivery tickets with their preceding invoices. Multiple delivery tickets
           for the same invoice will all be merged with that invoice.

        Args:
            text (str): Raw text containing one or multiple invoices

        Returns:
            list: List of processed invoice texts, with delivery tickets merged into their corresponding invoices

        Example:
            >>> text = '''
            Invoice Number: 001
            Content...
            Invoice Number: 002
            Invoice Type: Delivery ticket
            Content...
            Invoice Number: 003
            Invoice Type: Delivery ticket
            Content...
            Invoice Number: 004
            Content...
            '''
            >>> processor = InvoiceProcessor()
            >>> invoices = processor.split_invoices(text)
            >>> # Returns [Invoice001 + DeliveryTicket002 + DeliveryTicket003, Invoice004]
        """
        lines = text.split('\n')
        invoices = []
        current_invoice_text = ''
        current_invoice_number = None

        for line in lines:
            line = line.strip()
            # Buscar 'Invoice Number' con o sin asteriscos
            match = re.match(r'-\s*\**Invoice Number:?\**\s*(.*)', line, re.IGNORECASE)
            if match:
                invoice_number = match.group(1).strip()
                if invoice_number and invoice_number != current_invoice_number:
                    if current_invoice_number is not None:
                        # Si ya hemos iniciado una factura previa, agregamos el texto acumulado a la lista
                        if current_invoice_text.strip():
                            invoices.append(current_invoice_text.strip())
                        # Iniciamos una nueva factura
                        current_invoice_text = ''
                    current_invoice_number = invoice_number
                # Añadimos la línea actual al texto de la factura
                current_invoice_text += '\n' + line
            else:
                current_invoice_text += '\n' + line

        if current_invoice_text.strip():
            invoices.append(current_invoice_text.strip())

        # Define page-only document types (documents that only contribute page numbers, not content)
        page_only_document_types = {
            'delivery ticket',
            'daily job tracking form'
        }

        # Merge page-only documents with their preceding invoices
        merged_invoices = []
        i = 0
        while i < len(invoices):
            current_invoice = invoices[i]
            # Check if next invoice is a page-only document
            if i + 1 < len(invoices):
                next_invoice = invoices[i + 1]

                # Check for any page-only document type
                is_page_only = False
                for doc_type in page_only_document_types:
                    if f"Invoice Type: {doc_type.title()}" in next_invoice:
                        is_page_only = True
                        break

                if is_page_only:
                    merged_invoices.append(current_invoice + "\n\n" + next_invoice)
                    i += 2
                else:
                    merged_invoices.append(current_invoice)
                    i += 1
            else:
                merged_invoices.append(current_invoice)
                i += 1

        return merged_invoices


    def extract_invoice_type(self, invoice_text: str) -> str:
        """
        Extract invoice type from processed text.

        Args:
            invoice_text (str): Processed invoice text

        Returns:
            str: Invoice type or "Purchase Invoice" as default
        """
        pattern = re.compile(r"Invoice Type:\s*(.*)")
        match = pattern.search(invoice_text)
        invoice_type = match.group(1).strip() if match else "Purchase Invoice"
        return invoice_type

    def select_invoice_model(self, invoice_type: str):
        match invoice_type:
            case "Electricity Bill":
                InvoiceModel = InvoiceElectric
            case "Water Bill":
                InvoiceModel = InvoiceWater
            case "Gas Bill":
                InvoiceModel = InvoiceGas
            case _:
                InvoiceModel = InvoicePurchase

        return InvoiceModel

    async def get_structured_data(self, invoice_text: str, InvoiceModel: type[BaseModel]):
        """
        Extract structured data from invoice text using language model.

        Implements retry logic for rate limit handling.

        Args:
            invoice_text (str): Processed invoice text
            InvoiceModel (type[BaseModel]): Pydantic model to structure the data

        Returns:
            dict: Structured invoice data

        Raises:
            ValidationError: If data doesn't match model
            HTTPException: On rate limit or processing errors
        """
        prompt = """
        You will receive a text containing information about an invoice.
        Your goal is to extract structured information from the provided invoice.
        Focus on identifying key details such as 'invoice_number', 'issued_by', 'billing_date',
        'due_date', 'total_amount_due', 'items_list', and any specific charges relevant to the invoice type.
        Ensure accuracy and completeness in your extraction.

        take into account the structured model you are provided
        """

        data = f"Here is the invoice text: {invoice_text}"
        max_retries = 3  # Número máximo de reintentos
        for attempt in range(max_retries):
            try:
                response = await self.langchain_client.get_structured_data(InvoiceModel, data, prompt)
                return response['response']

            except ValidationError as e:
                raise ValueError(f"Model validation error: {e}")

            except HTTPException as e:
                raise e

            except Exception as e:
                if attempt < max_retries - 1:
                    print(e)
                    print("Rate limit reached, waiting 10 seconds before retrying...")
                    await asyncio.sleep(10)
                else:
                    print(e)
                    raise HTTPException(status_code=429, detail=f"Requests limit reached, please try again later. {e}")
