# Daily Job Tracking Form Implementation

## Overview

This document describes the implementation of "Daily Job Tracking Form" support in the invoice processing system, along with a flexible framework for adding more document types that should receive the same treatment as delivery tickets.

## What Was Implemented

### 1. **Flexible Page-Only Document System**

Created a configurable system that treats certain document types as "page-only" documents - meaning only their page numbers are captured and associated with the preceding invoice, while their content is ignored.

### 2. **Added "Daily Job Tracking Form" Support**

The system now recognizes "Daily job tracking form" as a page-only document type, giving it the same treatment as delivery tickets.

### 3. **Future-Proof Design**

The implementation allows for easy addition of new document types without code changes, using a simple configuration approach.

## Files Modified

### 1. `services/processors/facturius/invoice_processor_test.py`

**Key Changes:**
- Added `PAGE_ONLY_DOCUMENT_TYPES` class constant
- Created `is_page_only_document()` method for flexible document type checking
- Added `add_page_only_document_type()` method for runtime additions
- Updated prompts to include "Daily job tracking form"
- Modified merge logic to use the flexible system
- Added `page_only_pages` field alongside existing `delivery_ticket_pages` for backward compatibility

**New Methods:**
```python
def is_page_only_document(self, invoice_type: str) -> bool:
    """Check if a document type should only contribute page numbers, not content."""
    
def add_page_only_document_type(self, document_type: str) -> None:
    """Add a new document type that should only contribute page numbers."""
```

### 2. `services/processors/facturius/invoice_processor.py`

**Key Changes:**
- Updated prompt to include "Daily job tracking form"
- Modified `split_invoices()` method to handle multiple page-only document types
- Added flexible document type checking in merge logic

### 3. `domain/models/facturius/invoice_model.py`

**Key Changes:**
- Updated `invoice_type` field description to include "Daily job tracking form"

## How It Works

### 1. **Document Type Detection**

The system now checks if a document type is in the `page_only_document_types` set:

```python
PAGE_ONLY_DOCUMENT_TYPES = {
    'delivery ticket',
    'daily job tracking form'
}
```

### 2. **Processing Logic**

When a page-only document is detected:
1. **Page number is extracted** and stored
2. **Content is ignored** (not merged into invoice data)
3. **Page number is associated** with the preceding invoice
4. **Document page range is updated** to include the page-only document page

### 3. **Data Structure**

The system maintains two fields for tracking page-only documents:
- `page_only_pages`: New general field for all page-only document types
- `delivery_ticket_pages`: Maintained for backward compatibility

### 4. **Backward Compatibility**

Existing delivery ticket functionality remains unchanged:
- All existing `delivery_ticket_pages` logic still works
- New `page_only_pages` field provides the general solution
- Both fields are properly merged and included in `document_page_range`

## Usage Examples

### Adding a New Page-Only Document Type

```python
# At runtime, you can add new document types
processor.add_page_only_document_type("Work Order Form")
processor.add_page_only_document_type("Inspection Report")

# These will now be treated the same as delivery tickets
```

### Checking if a Document Type is Page-Only

```python
if processor.is_page_only_document("Daily job tracking form"):
    # This document will only contribute its page number
    pass
```

## Prompt Updates

All prompts now include the new document type:

**Before:**
```
- Use ONLY one of these valid types: 'Delivery ticket', 'Credit Note', 'Purchase', 'Other'
```

**After:**
```
- Use ONLY one of these valid types: 'Delivery ticket', 'Daily job tracking form', 'Credit Note', 'Purchase', 'Other'
```

## Benefits

### 1. **Consistency**
- "Daily job tracking form" receives identical treatment to delivery tickets
- Unified approach for all page-only document types

### 2. **Flexibility**
- Easy to add new document types without code changes
- Runtime configuration possible

### 3. **Maintainability**
- Single source of truth for page-only document types
- Centralized logic for handling these documents

### 4. **Backward Compatibility**
- Existing delivery ticket functionality preserved
- No breaking changes to existing data structures

## Testing

The implementation was tested with:
- Case-insensitive document type detection
- Addition of new document types at runtime
- Proper identification of page-only vs. regular documents
- Backward compatibility with existing delivery ticket logic

## Future Enhancements

To add more page-only document types in the future:

1. **Static Addition** (requires code change):
   ```python
   PAGE_ONLY_DOCUMENT_TYPES = {
       'delivery ticket',
       'daily job tracking form',
       'new document type'  # Add here
   }
   ```

2. **Dynamic Addition** (no code change required):
   ```python
   processor.add_page_only_document_type("New Document Type")
   ```

3. **Configuration File** (future enhancement):
   Could be extended to read from a configuration file for even more flexibility.

## Summary

The implementation successfully adds "Daily job tracking form" support while creating a flexible, maintainable system for handling similar document types in the future. The solution maintains full backward compatibility and provides a clean path for future enhancements.
