Procesando CV como imágenes para extraer datos estructurados directamente.
Documento con 5 páginas detectado.

This is the first or only page of the CV, so it likely contains personal information,
                                summary, and possibly the start of education or work experience sections.

                                CRITICAL: For each work experience, extract ALL responsibilities listed. Do not omit any responsibilities
                                or summarize them - include each bullet point as a separate item in the responsibilities list. If there
                                are multiple bullet points with responsibilities, make sure to capture every single one. The responsibilities field
                                should NEVER be null if there are bullet points in the text.

                                When you see "Key responsibilities:" followed by bullet points, these are responsibilities for the most recently
                                mentioned job title and company. Make sure to extract ALL of these bullet points into the responsibilities field.

                                IMPORTANT: If a work experience entry appears to be cut off at the end of the page (for example, if the last bullet point
                                is incomplete or if there are clearly more responsibilities that continue onto the next page), still extract all the
                                information available on this page. The system will merge the information from subsequent pages.

R: {
  "personal_info": {
    "full_name": "UMA SEKARAN",
    "country": null,
    "city": null,
    "address": null,
    "phone_number": "****** 394 6602",
    "email": "<EMAIL>",
    "linkedin_profile": null,
    "website": null
  },
  "summary": "I am an IT Business Data Analyst with 16 years of Techno functional experience in enabling software and analytical solutions to Investment Banking domain, 
Printing and HR domain. Gained 6+ months experience working in US for clients like US Bank and American Crystal Sugar. Have acquired diversified experience in Consulting, Agile, Data strategy and integration, Data analysis using SQL and Excel, Operations management, VBA macros, Data mining and analytics using Tableau, enabling SaaS solution with Cloud data migration validation, API flow check, Requirements gathering, Testing, Stakeholder management and Production support. Have valid L2S visa till Aug 2026 to work remotely or hybrid from nearby city (Fargo, ND) of Jamestown, North Dakota – USA.",
  "education": null,
  "work_experience": [
    {
      "job_title": "Technical Consultant, Platform Test Analyst",
      "company_name": "Apex systems inc, Chicago",
      "start_date": "July 2024",
      "end_date": "Present",
      "location": null,
      "responsibilities": [
        "Process Documentation & Validation – got involved in validating the process, following the data mapping documents, functional validation, reporting and tracking to closure of the Reconciliation system for the Regulatory reporting data between Trade booking systems (Wall Street and Calypso) and DTCC Hub reporting system",       
        "Performed UAT on GUI platform developed for Asset management for US bank, by asset class type– aggregated and individual detail, grouping and filtration based on reporting filter values, authorization and authentication",
        "Wall Street – Performed FX trade booking (Spot, Forward, NDF, Swaps), ensured test coverage, verified the trade details on the DB tables involved and validated 
them in the new SDR reporting solution",
        "Performed validations of trades/XML reported on DTCC hub and in-house reporting platform for Trade state, RT and valuation messages"
      ],
      "skills": [
        {
          "name": "Wall Street 5.1.4, Calypso 17, DTCC hub, Postgres, MS SQL server, SQL, Excel",
          "proficiency_level": null,
          "years_of_experience": null
        }
      ]
    },
    {
      "job_title": "Business Analyst",
      "company_name": "Pro Source, Fargo, North Dakota",
      "start_date": "May 2024",
      "end_date": "June 2024",
      "location": null,
      "responsibilities": [
        "The project was on building a cloud based application and migrating data from a legacy mainframe application with Oracle DB and SAP BO reports, that served as a beet procurement, storage, seed distribution, contracts, acres associated, invoices, etc., for American Crystal Sugar, ND"
      ],
      "skills": []
    }
  ],
  "roles": null,
  "skills": [
    {
      "name": "Requirements Management: ALM, JIRA, Confluence",
      "proficiency_level": null,
      "years_of_experience": null
    },
    {
      "name": "Software Languages: VB, C++, Java – analyze code and interpret logic",
      "proficiency_level": null,
      "years_of_experience": null
    },
    {
      "name": "Office Tools: Word, Excel, PowerPoint, Visio, Outlook, SharePoint",
      "proficiency_level": null,
      "years_of_experience": null
    },
    {
      "name": "Excel – pivots, macros, lookups, lists, charts, VBA macros, data analytics",
      "proficiency_level": null,
      "years_of_experience": null
    },
    {
      "name": "Database – SQL, MySQL, Snowflake, PostgreSQL, Oracle, Teradata, Big Query",
      "proficiency_level": null,
      "years_of_experience": null
    },
    {
      "name": "Data visualization – Tableau and Spotfire",
      "proficiency_level": null,
      "years_of_experience": null
    }
  ],
  "soft_skills": null,
  "certifications": null,
  "languages": null,
  "projects": null,
  "references": null,
  "hobbies_interests": null
}
---------------------------------------------------------------------
Datos extraídos de la página 1 (primera página)

A: Extract all structured information from this CV page.
                                This is page 2 of the CV, which likely continues from previous pages.

                                IMPORTANT: If you see information that appears to be a continuation of a work experience
                                entry from a previous page (such as responsibilities, client details, or project information
                                for a job title already mentioned), make sure to include the job title and company name
                                exactly as they appeared on the previous page, along with the new information.

                                For example, if page 1 had a work experience entry for "Senior Consultant" at "Capco Technologies"
                                and this page contains additional details like client information or responsibilities for that same role,
                                include both the job title "Senior Consultant", company name "Capco Technologies", AND the new information.

                                CRITICAL: For each work experience, extract ALL responsibilities listed. Do not omit any responsibilities
                                or summarize them - include each bullet point as a separate item in the responsibilities list. If there
                                are multiple bullet points with responsibilities, make sure to capture every single one. The responsibilities field
                                should NEVER be null if there are bullet points in the text.

                                VERY IMPORTANT: If this page starts with client information (like "Client: JP Morgan, EMEA"), project scope,
                                or bullet points that appear to be a continuation from a previous page's work experience, treat it as a continuation
                                of the last work experience entry from the previous page. Include the job title and company name from the previous page,
                                and add these details to that work experience.

                                For example, if this page starts with "Client: JP Morgan, EMEA" or "Project Scope: Letter of Credit application..."
                                and doesn't mention a job title, it's likely a continuation of the last work experience from the previous page.
                                In this case, you should include the job title "Senior Consultant" and company name "Capco Technologies" from
                                the previous page, along with the client and project information from this page.

                                EXTREMELY IMPORTANT: If you see bullet points at the beginning of the page without a clear job title or dates, these are almost
                                certainly responsibilities that belong to the last work experience from the previous page. In this case, make sure to:
                                1. Include the job title and company name from the previous page
                                2. Add ALL these bullet points to the responsibilities list for that job
                                3. Do NOT create a new work experience entry for these bullet points
                                4. The responsibilities field should NEVER be empty or null when bullet points are present

                                IMPORTANT: If you see a new job title with start and end dates (like "Designation: Senior Business Analyst" followed by
                                "Organization: Wipro Technology" with dates), this is a NEW job entry, not a continuation of the previous one. Create a
                                separate work experience entry for this job with its own responsibilities.

                                When you see "Key responsibilities:" followed by bullet points, these are responsibilities for the most recently
                                mentioned job title and company. Make sure to extract ALL of these bullet points into the responsibilities field.

                                CRITICAL FOR WORK EXPERIENCE SPANNING PAGES: If you see text like "o Data analysis – expertise in writing medium to complex SQL queries" 
                                at the beginning of the page without a job title, this is likely a continuation of responsibilities from the previous page.
                                In this case, include the job title and company name from the previous page, and add these responsibilities to that job.

                                IMPORTANT FOR MULTI-PAGE RESPONSIBILITIES: If a responsibility appears to be a continuation from the previous page
                                (for example, if it starts with lowercase letters or continues a thought), still extract it as a complete responsibility.
                                The system will handle merging and deduplication of responsibilities.

                                Focus on any new information not captured from previous pages, such as additional
                                work experiences, skills, certifications, or other sections.

I already have some information extracted: {
  "personal_info": {
    "full_name": "UMA SEKARAN",
    "country": null,
    "city": null,
    "address": null,
    "phone_number": "****** 394 6602",
    "email": "<EMAIL>",
    "linkedin_profile": null,
    "website": null
  },
  "summary": "I am an IT Business Data Analyst with 16 years of Techno functional experience in enabling software and analytical solutions to Investment Banking domain, 
Printing and HR domain. Gained 6+ months experience working in US for clients like US Bank and American Crystal Sugar. Have acquired diversified experience in Consulting, Agile, Data strategy and integration, Data analysis using SQL and Excel, Operations management, VBA macros, Data mining and analytics using Tableau, enabling SaaS solution with Cloud data migration validation, API flow check, Requirements gathering, Testing, Stakeholder management and Production support. Have valid L2S visa till Aug 2026 to work remotely or hybrid from nearby city (Fargo, ND) of Jamestown, North Dakota – USA.",
  "education": null,
  "work_experience": [
    {
      "job_title": "Technical Consultant, Platform Test Analyst",
      "company_name": "Apex systems inc, Chicago",
      "start_date": "July 2024",
      "end_date": "Present",
      "location": null,
      "responsibilities": [
        "Process Documentation & Validation – got involved in validating the process, following the data mapping documents, functional validation, reporting and tracking to closure of the Reconciliation system for the Regulatory reporting data between Trade booking systems (Wall Street and Calypso) and DTCC Hub reporting system",       
        "Performed UAT on GUI platform developed for Asset management for US bank, by asset class type– aggregated and individual detail, grouping and filtration based on reporting filter values, authorization and authentication",
        "Wall Street – Performed FX trade booking (Spot, Forward, NDF, Swaps), ensured test coverage, verified the trade details on the DB tables involved and validated 
them in the new SDR reporting solution",
        "Performed validations of trades/XML reported on DTCC hub and in-house reporting platform for Trade state, RT and valuation messages"
      ],
      "skills": [
        {
          "name": "Wall Street 5.1.4, Calypso 17, DTCC hub, Postgres, MS SQL server, SQL, Excel",
          "proficiency_level": null,
          "years_of_experience": null
        }
      ]
    },
    {
      "job_title": "Business Analyst",
      "company_name": "Pro Source, Fargo, North Dakota",
      "start_date": "May 2024",
      "end_date": "June 2024",
      "location": null,
      "responsibilities": [
        "The project was on building a cloud based application and migrating data from a legacy mainframe application with Oracle DB and SAP BO reports, that served as a beet procurement, storage, seed distribution, contracts, acres associated, invoices, etc., for American Crystal Sugar, ND"
      ],
      "skills": []
    }
  ],
  "roles": null,
  "skills": [
    {
      "name": "Requirements Management: ALM, JIRA, Confluence",
      "proficiency_level": null,
      "years_of_experience": null
    },
    {
      "name": "Software Languages: VB, C++, Java – analyze code and interpret logic",
      "proficiency_level": null,
      "years_of_experience": null
    },
    {
      "name": "Office Tools: Word, Excel, PowerPoint, Visio, Outlook, SharePoint",
      "proficiency_level": null,
      "years_of_experience": null
    },
    {
      "name": "Excel – pivots, macros, lookups, lists, charts, VBA macros, data analytics",
      "proficiency_level": null,
      "years_of_experience": null
    },
    {
      "name": "Database – SQL, MySQL, Snowflake, PostgreSQL, Oracle, Teradata, Big Query",
      "proficiency_level": null,
      "years_of_experience": null
    },
    {
      "name": "Data visualization – Tableau and Spotfire",
      "proficiency_level": null,
      "years_of_experience": null
    }
  ],
  "soft_skills": null,
  "certifications": null,
  "languages": null,
  "projects": null,
  "references": null,
  "hobbies_interests": null
}

Please fill in any missing fields or correct any errors based on the image, while preserving the existing data.
R: {
  "personal_info": null,
  "summary": null,
  "education": null,
  "work_experience": [
    {
      "job_title": "Lead Business Analyst",
      "company_name": "Cap Gemini",
      "start_date": "Dec 2021",
      "end_date": "Aug 2023",
      "location": "Bengaluru, India",
      "responsibilities": [
        "Stakeholder co-ordination– organized calls, elicit requirements, conducted workshops with showcasing prototypes, gave demo on new enhanced application before UAT, prepared PPT slides for sprint & quarterly review, share weekly updates to Product owner and Sponsors, coordinated with SMEs, vendors, external & cross functional teams, Development team, proactive anticipation of issues",
        "Product Owner – performed the role for implementing specific features, track and ensure on-time delivery of new features/enhancements/bug fix, within timelines 
and budget estimated based on FTEs (Full Time Equivalent), practiced Six sigma methodology",
        "Release Management –perform UAT validations, timeline Test evidence , record keeping, Demo to clients, Plan release steps with DEV & Testing team",
        "Requirements & Process Document –Creation and maintenance of Business Requirement Document, Data strategy and Process Flow diagrams, Entity Relationship(ER) diagram, End to end design & review of Data flow architecture diagram, Mockup screens for User review, Workflow diagrams for each type and group of user",
        "Impact analysis, Test plan, Effort estimation, Project roadmap, Backlogs - creation & maintenance, Root cause analysis(RCA), Risk management strategy, Risk mitigation plan and Asset management strategy",
        "BA validations – AS IS – TO BE method, identify user stories without loopholes, break down to tasks, review test scope, test cases, perform functional, system, 
load and stress testing, Regression, User Acceptance Testing (UAT) & PROD checks, Fitness page preparation & validation as per Behavior Driven Development (BDD), automation of testing and processes using Excel and SQL",
        "Agile methodology – practice agile ceremonies like Daily scrum, Sprint planning, review & demo, retrospective, track all tasks & activities on JIRA by Epic, Story & Task, backlog maintenance, JIRA board cleanup, HP Application Lifecycle Management tool (ALM) version 11",
        "Data analytics – analyzed historical data, understood data trends and patterns, forecast data using excel, strategize the data monitoring & reporting on client 
portfolio based on risk parameters",
        "Data profiling – understand and identify the type and nature of data, build/ track/maintain Data mapping (between source and intermediate systems to destination) information, maintain Data dictionary",
        "Data flow – secure data access, limited access as per entitlements, authentication, transfer, loading, Staging, Cleansing, Data transformation (between source and target)",
        "Data integration (external data merging into existing dataset, prepare for DWH cube, archival and retention policies",
        "Data management – understood and monitored master and transaction tables, audit tables, parameter tables and history tables, considering referential integrity and avoiding duplicates. Archive the historical data without much loss, readily accessible and available for future use",
        "Data reporting and Reconciliation reporting – for risk assessment and constant monitoring"
      ],
      "skills": [
        {
          "name": "SQL",
          "proficiency_level": "Advanced",
          "years_of_experience": null
        },
        {
          "name": "Excel",
          "proficiency_level": "Advanced",
          "years_of_experience": null
        },
        {
          "name": "Agile methodology",
          "proficiency_level": "Advanced",
          "years_of_experience": null
        },
        {
          "name": "JIRA",
          "proficiency_level": "Advanced",
          "years_of_experience": null
        },
        {
          "name": "Data Analytics",
          "proficiency_level": "Advanced",
          "years_of_experience": null
        }
      ]
    },
    {
      "job_title": "Responsibilities Continuation",
      "company_name": "Cap Gemini",
      "start_date": null,
      "end_date": null,
      "location": null,
      "responsibilities": [
        "Requirements & Process Documentation –Conducted requirements elicitation sessions with end users of existing application, got the doubts and questions clarified from end users, involved in the creation of Business Requirement Document and Process Flow diagrams, Mockup screens for User review, Workflow diagrams for each type and group of user",
        "Design Analysis – Analyzed the SAP BO report design, identified the DB tables involved and joins created"
      ],
      "skills": null
    }
  ],
  "roles": null,
  "skills": null,
  "soft_skills": null,
  "certifications": null,
  "languages": null,
  "projects": null,
  "references": null,
  "hobbies_interests": null
}
---------------------------------------------------------------------

Datos acumulados antes de la fusión: {
  "personal_info": {
    "full_name": "UMA SEKARAN",
    "country": null,
    "city": null,
    "address": null,
    "phone_number": "****** 394 6602",
    "email": "<EMAIL>",
    "linkedin_profile": null,
    "website": null
  },
  "summary": "I am an IT Business Data Analyst with 16 years of Techno functional experience in enabling software and analytical solutions to Investment Banking domain, 
Printing and HR domain. Gained 6+ months experience working in US for clients like US Bank and American Crystal Sugar. Have acquired diversified experience in Consulting, Agile, Data strategy and integration, Data analysis using SQL and Excel, Operations management, VBA macros, Data mining and analytics using Tableau, enabling SaaS solution with Cloud data migration validation, API flow check, Requirements gathering, Testing, Stakeholder management and Production support. Have valid L2S visa till Aug 2026 to work remotely or hybrid from nearby city (Fargo, ND) of Jamestown, North Dakota – USA.",
  "education": null,
  "work_experience": [
    {
      "job_title": "Technical Consultant, Platform Test Analyst",
      "company_name": "Apex systems inc, Chicago",
      "start_date": "July 2024",
      "end_date": "Present",
      "location": null,
      "responsibilities": [
        "Process Documentation & Validation – got involved in validating the process, following the data mapping documents, functional validation, reporting and tracking to closure of the Reconciliation system for the Regulatory reporting data between Trade booking systems (Wall Street and Calypso) and DTCC Hub reporting system",       
        "Performed UAT on GUI platform developed for Asset management for US bank, by asset class type– aggregated and individual detail, grouping and filtration based on reporting filter values, authorization and authentication",
        "Wall Street – Performed FX trade booking (Spot, Forward, NDF, Swaps), ensured test coverage, verified the trade details on the DB tables involved and validated 
them in the new SDR reporting solution",
        "Performed validations of trades/XML reported on DTCC hub and in-house reporting platform for Trade state, RT and valuation messages"
      ],
      "skills": [
        {
          "name": "Wall Street 5.1.4, Calypso 17, DTCC hub, Postgres, MS SQL server, SQL, Excel",
          "proficiency_level": null,
          "years_of_experience": null
        }
      ]
    },
    {
      "job_title": "Business Analyst",
      "company_name": "Pro Source, Fargo, North Dakota",
      "start_date": "May 2024",
      "end_date": "June 2024",
      "location": null,
      "responsibilities": [
        "The project was on building a cloud based application and migrating data from a legacy mainframe application with Oracle DB and SAP BO reports, that served as a beet procurement, storage, seed distribution, contracts, acres associated, invoices, etc., for American Crystal Sugar, ND"
      ],
      "skills": []
    }
  ],
  "roles": null,
  "skills": [
    {
      "name": "Requirements Management: ALM, JIRA, Confluence",
      "proficiency_level": null,
      "years_of_experience": null
    },
    {
      "name": "Software Languages: VB, C++, Java – analyze code and interpret logic",
      "proficiency_level": null,
      "years_of_experience": null
    },
    {
      "name": "Office Tools: Word, Excel, PowerPoint, Visio, Outlook, SharePoint",
      "proficiency_level": null,
      "years_of_experience": null
    },
    {
      "name": "Excel – pivots, macros, lookups, lists, charts, VBA macros, data analytics",
      "proficiency_level": null,
      "years_of_experience": null
    },
    {
      "name": "Database – SQL, MySQL, Snowflake, PostgreSQL, Oracle, Teradata, Big Query",
      "proficiency_level": null,
      "years_of_experience": null
    },
    {
      "name": "Data visualization – Tableau and Spotfire",
      "proficiency_level": null,
      "years_of_experience": null
    }
  ],
  "soft_skills": null,
  "certifications": null,
  "languages": null,
  "projects": null,
  "references": null,
  "hobbies_interests": null
}

Nuevos datos de la página 2: {
  "personal_info": null,
  "summary": null,
  "education": null,
  "work_experience": [
    {
      "job_title": "Lead Business Analyst",
      "company_name": "Cap Gemini",
      "start_date": "Dec 2021",
      "end_date": "Aug 2023",
      "location": "Bengaluru, India",
      "responsibilities": [
        "Stakeholder co-ordination– organized calls, elicit requirements, conducted workshops with showcasing prototypes, gave demo on new enhanced application before UAT, prepared PPT slides for sprint & quarterly review, share weekly updates to Product owner and Sponsors, coordinated with SMEs, vendors, external & cross functional teams, Development team, proactive anticipation of issues",
        "Product Owner – performed the role for implementing specific features, track and ensure on-time delivery of new features/enhancements/bug fix, within timelines 
and budget estimated based on FTEs (Full Time Equivalent), practiced Six sigma methodology",
        "Release Management –perform UAT validations, timeline Test evidence , record keeping, Demo to clients, Plan release steps with DEV & Testing team",
        "Requirements & Process Document –Creation and maintenance of Business Requirement Document, Data strategy and Process Flow diagrams, Entity Relationship(ER) diagram, End to end design & review of Data flow architecture diagram, Mockup screens for User review, Workflow diagrams for each type and group of user",
        "Impact analysis, Test plan, Effort estimation, Project roadmap, Backlogs - creation & maintenance, Root cause analysis(RCA), Risk management strategy, Risk mitigation plan and Asset management strategy",
        "BA validations – AS IS – TO BE method, identify user stories without loopholes, break down to tasks, review test scope, test cases, perform functional, system, 
load and stress testing, Regression, User Acceptance Testing (UAT) & PROD checks, Fitness page preparation & validation as per Behavior Driven Development (BDD), automation of testing and processes using Excel and SQL",
        "Agile methodology – practice agile ceremonies like Daily scrum, Sprint planning, review & demo, retrospective, track all tasks & activities on JIRA by Epic, Story & Task, backlog maintenance, JIRA board cleanup, HP Application Lifecycle Management tool (ALM) version 11",
        "Data analytics – analyzed historical data, understood data trends and patterns, forecast data using excel, strategize the data monitoring & reporting on client 
portfolio based on risk parameters",
        "Data profiling – understand and identify the type and nature of data, build/ track/maintain Data mapping (between source and intermediate systems to destination) information, maintain Data dictionary",
        "Data flow – secure data access, limited access as per entitlements, authentication, transfer, loading, Staging, Cleansing, Data transformation (between source and target)",
        "Data integration (external data merging into existing dataset, prepare for DWH cube, archival and retention policies",
        "Data management – understood and monitored master and transaction tables, audit tables, parameter tables and history tables, considering referential integrity and avoiding duplicates. Archive the historical data without much loss, readily accessible and available for future use",
        "Data reporting and Reconciliation reporting – for risk assessment and constant monitoring"
      ],
      "skills": [
        {
          "name": "SQL",
          "proficiency_level": "Advanced",
          "years_of_experience": null
        },
        {
          "name": "Excel",
          "proficiency_level": "Advanced",
          "years_of_experience": null
        },
        {
          "name": "Agile methodology",
          "proficiency_level": "Advanced",
          "years_of_experience": null
        },
        {
          "name": "JIRA",
          "proficiency_level": "Advanced",
          "years_of_experience": null
        },
        {
          "name": "Data Analytics",
          "proficiency_level": "Advanced",
          "years_of_experience": null
        }
      ]
    },
    {
      "job_title": "Responsibilities Continuation",
      "company_name": "Cap Gemini",
      "start_date": null,
      "end_date": null,
      "location": null,
      "responsibilities": [
        "Requirements & Process Documentation –Conducted requirements elicitation sessions with end users of existing application, got the doubts and questions clarified from end users, involved in the creation of Business Requirement Document and Process Flow diagrams, Mockup screens for User review, Workflow diagrams for each type and group of user",
        "Design Analysis – Analyzed the SAP BO report design, identified the DB tables involved and joins created"
      ],
      "skills": null
    }
  ],
  "roles": null,
  "skills": null,
  "soft_skills": null,
  "certifications": null,
  "languages": null,
  "projects": null,
  "references": null,
  "hobbies_interests": null
}

Datos fusionados: {
  "personal_info": {
    "full_name": "UMA SEKARAN",
    "country": null,
    "city": null,
    "address": null,
    "phone_number": "****** 394 6602",
    "email": "<EMAIL>",
    "linkedin_profile": null,
    "website": null
  },
  "summary": "I am an IT Business Data Analyst with 16 years of Techno functional experience in enabling software and analytical solutions to Investment Banking domain, 
Printing and HR domain. Gained 6+ months experience working in US for clients like US Bank and American Crystal Sugar. Have acquired diversified experience in Consulting, Agile, Data strategy and integration, Data analysis using SQL and Excel, Operations management, VBA macros, Data mining and analytics using Tableau, enabling SaaS solution with Cloud data migration validation, API flow check, Requirements gathering, Testing, Stakeholder management and Production support. Have valid L2S visa till Aug 2026 to work remotely or hybrid from nearby city (Fargo, ND) of Jamestown, North Dakota – USA.",
  "education": null,
  "work_experience": [
    {
      "job_title": "Technical Consultant, Platform Test Analyst",
      "company_name": "Apex systems inc, Chicago",
      "start_date": "July 2024",
      "end_date": "Present",
      "location": null,
      "responsibilities": [
        "Process Documentation & Validation – got involved in validating the process, following the data mapping documents, functional validation, reporting and tracking to closure of the Reconciliation system for the Regulatory reporting data between Trade booking systems (Wall Street and Calypso) and DTCC Hub reporting system",       
        "Performed UAT on GUI platform developed for Asset management for US bank, by asset class type– aggregated and individual detail, grouping and filtration based on reporting filter values, authorization and authentication",
        "Wall Street – Performed FX trade booking (Spot, Forward, NDF, Swaps), ensured test coverage, verified the trade details on the DB tables involved and validated 
them in the new SDR reporting solution",
        "Performed validations of trades/XML reported on DTCC hub and in-house reporting platform for Trade state, RT and valuation messages"
      ],
      "skills": [
        {
          "name": "Wall Street 5.1.4, Calypso 17, DTCC hub, Postgres, MS SQL server, SQL, Excel",
          "proficiency_level": null,
          "years_of_experience": null
        }
      ]
    },
    {
      "job_title": "Business Analyst",
      "company_name": "Pro Source, Fargo, North Dakota",
      "start_date": "May 2024",
      "end_date": "June 2024",
      "location": null,
      "responsibilities": [
        "The project was on building a cloud based application and migrating data from a legacy mainframe application with Oracle DB and SAP BO reports, that served as a beet procurement, storage, seed distribution, contracts, acres associated, invoices, etc., for American Crystal Sugar, ND"
      ],
      "skills": []
    },
    {
      "job_title": "Lead Business Analyst",
      "company_name": "Cap Gemini",
      "start_date": "Dec 2021",
      "end_date": "Aug 2023",
      "location": "Bengaluru, India",
      "responsibilities": [
        "Stakeholder co-ordination– organized calls, elicit requirements, conducted workshops with showcasing prototypes, gave demo on new enhanced application before UAT, prepared PPT slides for sprint & quarterly review, share weekly updates to Product owner and Sponsors, coordinated with SMEs, vendors, external & cross functional teams, Development team, proactive anticipation of issues",
        "Product Owner – performed the role for implementing specific features, track and ensure on-time delivery of new features/enhancements/bug fix, within timelines 
and budget estimated based on FTEs (Full Time Equivalent), practiced Six sigma methodology",
        "Release Management –perform UAT validations, timeline Test evidence , record keeping, Demo to clients, Plan release steps with DEV & Testing team",
        "Requirements & Process Document –Creation and maintenance of Business Requirement Document, Data strategy and Process Flow diagrams, Entity Relationship(ER) diagram, End to end design & review of Data flow architecture diagram, Mockup screens for User review, Workflow diagrams for each type and group of user",
        "Impact analysis, Test plan, Effort estimation, Project roadmap, Backlogs - creation & maintenance, Root cause analysis(RCA), Risk management strategy, Risk mitigation plan and Asset management strategy",
        "BA validations – AS IS – TO BE method, identify user stories without loopholes, break down to tasks, review test scope, test cases, perform functional, system, 
load and stress testing, Regression, User Acceptance Testing (UAT) & PROD checks, Fitness page preparation & validation as per Behavior Driven Development (BDD), automation of testing and processes using Excel and SQL",
        "Agile methodology – practice agile ceremonies like Daily scrum, Sprint planning, review & demo, retrospective, track all tasks & activities on JIRA by Epic, Story & Task, backlog maintenance, JIRA board cleanup, HP Application Lifecycle Management tool (ALM) version 11",
        "Data analytics – analyzed historical data, understood data trends and patterns, forecast data using excel, strategize the data monitoring & reporting on client 
portfolio based on risk parameters",
        "Data profiling – understand and identify the type and nature of data, build/ track/maintain Data mapping (between source and intermediate systems to destination) information, maintain Data dictionary",
        "Data flow – secure data access, limited access as per entitlements, authentication, transfer, loading, Staging, Cleansing, Data transformation (between source and target)",
        "Data integration (external data merging into existing dataset, prepare for DWH cube, archival and retention policies",
        "Data management – understood and monitored master and transaction tables, audit tables, parameter tables and history tables, considering referential integrity and avoiding duplicates. Archive the historical data without much loss, readily accessible and available for future use",
        "Data reporting and Reconciliation reporting – for risk assessment and constant monitoring"
      ],
      "skills": [
        {
          "name": "SQL",
          "proficiency_level": "Advanced",
          "years_of_experience": null
        },
        {
          "name": "Excel",
          "proficiency_level": "Advanced",
          "years_of_experience": null
        },
        {
          "name": "Agile methodology",
          "proficiency_level": "Advanced",
          "years_of_experience": null
        },
        {
          "name": "JIRA",
          "proficiency_level": "Advanced",
          "years_of_experience": null
        },
        {
          "name": "Data Analytics",
          "proficiency_level": "Advanced",
          "years_of_experience": null
        }
      ]
    },
    {
      "job_title": "Responsibilities Continuation",
      "company_name": "Cap Gemini",
      "start_date": null,
      "end_date": null,
      "location": null,
      "responsibilities": [
        "Requirements & Process Documentation –Conducted requirements elicitation sessions with end users of existing application, got the doubts and questions clarified from end users, involved in the creation of Business Requirement Document and Process Flow diagrams, Mockup screens for User review, Workflow diagrams for each type and group of user",
        "Design Analysis – Analyzed the SAP BO report design, identified the DB tables involved and joins created"
      ],
      "skills": null
    }
  ],
  "roles": null,
  "skills": [
    {
      "name": "Requirements Management: ALM, JIRA, Confluence",
      "proficiency_level": null,
      "years_of_experience": null
    },
    {
      "name": "Software Languages: VB, C++, Java – analyze code and interpret logic",
      "proficiency_level": null,
      "years_of_experience": null
    },
    {
      "name": "Office Tools: Word, Excel, PowerPoint, Visio, Outlook, SharePoint",
      "proficiency_level": null,
      "years_of_experience": null
    },
    {
      "name": "Excel – pivots, macros, lookups, lists, charts, VBA macros, data analytics",
      "proficiency_level": null,
      "years_of_experience": null
    },
    {
      "name": "Database – SQL, MySQL, Snowflake, PostgreSQL, Oracle, Teradata, Big Query",
      "proficiency_level": null,
      "years_of_experience": null
    },
    {
      "name": "Data visualization – Tableau and Spotfire",
      "proficiency_level": null,
      "years_of_experience": null
    }
  ],
  "soft_skills": null,
  "certifications": null,
  "languages": null,
  "projects": null,
  "references": null,
  "hobbies_interests": null
}
Actualizando datos con información de la página 2

A: Extract all structured information from this CV page.
                                This is page 3 of the CV, which likely continues from previous pages.

                                IMPORTANT: If you see information that appears to be a continuation of a work experience
                                entry from a previous page (such as responsibilities, client details, or project information
                                for a job title already mentioned), make sure to include the job title and company name
                                exactly as they appeared on the previous page, along with the new information.

                                For example, if page 2 had a work experience entry for "Senior Consultant" at "Capco Technologies"
                                and this page contains additional details like client information or responsibilities for that same role,
                                include both the job title "Senior Consultant", company name "Capco Technologies", AND the new information.

                                CRITICAL: For each work experience, extract ALL responsibilities listed. Do not omit any responsibilities
                                or summarize them - include each bullet point as a separate item in the responsibilities list. If there
                                are multiple bullet points with responsibilities, make sure to capture every single one. The responsibilities field
                                should NEVER be null if there are bullet points in the text.

                                VERY IMPORTANT: If this page starts with client information (like "Client: JP Morgan, EMEA"), project scope,
                                or bullet points that appear to be a continuation from a previous page's work experience, treat it as a continuation
                                of the last work experience entry from the previous page. Include the job title and company name from the previous page,
                                and add these details to that work experience.

                                For example, if this page starts with "Client: JP Morgan, EMEA" or "Project Scope: Letter of Credit application..."
                                and doesn't mention a job title, it's likely a continuation of the last work experience from the previous page.
                                In this case, you should include the job title "Senior Consultant" and company name "Capco Technologies" from
                                the previous page, along with the client and project information from this page.

                                EXTREMELY IMPORTANT: If you see bullet points at the beginning of the page without a clear job title or dates, these are almost
                                certainly responsibilities that belong to the last work experience from the previous page. In this case, make sure to:
                                1. Include the job title and company name from the previous page
                                2. Add ALL these bullet points to the responsibilities list for that job
                                3. Do NOT create a new work experience entry for these bullet points
                                4. The responsibilities field should NEVER be empty or null when bullet points are present

                                IMPORTANT: If you see a new job title with start and end dates (like "Designation: Senior Business Analyst" followed by
                                "Organization: Wipro Technology" with dates), this is a NEW job entry, not a continuation of the previous one. Create a
                                separate work experience entry for this job with its own responsibilities.

                                When you see "Key responsibilities:" followed by bullet points, these are responsibilities for the most recently
                                mentioned job title and company. Make sure to extract ALL of these bullet points into the responsibilities field.

                                CRITICAL FOR WORK EXPERIENCE SPANNING PAGES: If you see text like "o Data analysis – expertise in writing medium to complex SQL queries" 
                                at the beginning of the page without a job title, this is likely a continuation of responsibilities from the previous page.
                                In this case, include the job title and company name from the previous page, and add these responsibilities to that job.

                                IMPORTANT FOR MULTI-PAGE RESPONSIBILITIES: If a responsibility appears to be a continuation from the previous page
                                (for example, if it starts with lowercase letters or continues a thought), still extract it as a complete responsibility.
                                The system will handle merging and deduplication of responsibilities.

                                Focus on any new information not captured from previous pages, such as additional
                                work experiences, skills, certifications, or other sections.

I already have some information extracted: {
  "personal_info": {
    "full_name": "UMA SEKARAN",
    "country": null,
    "city": null,
    "address": null,
    "phone_number": "****** 394 6602",
    "email": "<EMAIL>",
    "linkedin_profile": null,
    "website": null
  },
  "summary": "I am an IT Business Data Analyst with 16 years of Techno functional experience in enabling software and analytical solutions to Investment Banking domain, 
Printing and HR domain. Gained 6+ months experience working in US for clients like US Bank and American Crystal Sugar. Have acquired diversified experience in Consulting, Agile, Data strategy and integration, Data analysis using SQL and Excel, Operations management, VBA macros, Data mining and analytics using Tableau, enabling SaaS solution with Cloud data migration validation, API flow check, Requirements gathering, Testing, Stakeholder management and Production support. Have valid L2S visa till Aug 2026 to work remotely or hybrid from nearby city (Fargo, ND) of Jamestown, North Dakota – USA.",
  "education": null,
  "work_experience": [
    {
      "job_title": "Technical Consultant, Platform Test Analyst",
      "company_name": "Apex systems inc, Chicago",
      "start_date": "July 2024",
      "end_date": "Present",
      "location": null,
      "responsibilities": [
        "Process Documentation & Validation – got involved in validating the process, following the data mapping documents, functional validation, reporting and tracking to closure of the Reconciliation system for the Regulatory reporting data between Trade booking systems (Wall Street and Calypso) and DTCC Hub reporting system",       
        "Performed UAT on GUI platform developed for Asset management for US bank, by asset class type– aggregated and individual detail, grouping and filtration based on reporting filter values, authorization and authentication",
        "Wall Street – Performed FX trade booking (Spot, Forward, NDF, Swaps), ensured test coverage, verified the trade details on the DB tables involved and validated 
them in the new SDR reporting solution",
        "Performed validations of trades/XML reported on DTCC hub and in-house reporting platform for Trade state, RT and valuation messages"
      ],
      "skills": [
        {
          "name": "Wall Street 5.1.4, Calypso 17, DTCC hub, Postgres, MS SQL server, SQL, Excel",
          "proficiency_level": null,
          "years_of_experience": null
        }
      ]
    },
    {
      "job_title": "Business Analyst",
      "company_name": "Pro Source, Fargo, North Dakota",
      "start_date": "May 2024",
      "end_date": "June 2024",
      "location": null,
      "responsibilities": [
        "The project was on building a cloud based application and migrating data from a legacy mainframe application with Oracle DB and SAP BO reports, that served as a beet procurement, storage, seed distribution, contracts, acres associated, invoices, etc., for American Crystal Sugar, ND"
      ],
      "skills": []
    },
    {
      "job_title": "Lead Business Analyst",
      "company_name": "Cap Gemini",
      "start_date": "Dec 2021",
      "end_date": "Aug 2023",
      "location": "Bengaluru, India",
      "responsibilities": [
        "Stakeholder co-ordination– organized calls, elicit requirements, conducted workshops with showcasing prototypes, gave demo on new enhanced application before UAT, prepared PPT slides for sprint & quarterly review, share weekly updates to Product owner and Sponsors, coordinated with SMEs, vendors, external & cross functional teams, Development team, proactive anticipation of issues",
        "Product Owner – performed the role for implementing specific features, track and ensure on-time delivery of new features/enhancements/bug fix, within timelines 
and budget estimated based on FTEs (Full Time Equivalent), practiced Six sigma methodology",
        "Release Management –perform UAT validations, timeline Test evidence , record keeping, Demo to clients, Plan release steps with DEV & Testing team",
        "Requirements & Process Document –Creation and maintenance of Business Requirement Document, Data strategy and Process Flow diagrams, Entity Relationship(ER) diagram, End to end design & review of Data flow architecture diagram, Mockup screens for User review, Workflow diagrams for each type and group of user",
        "Impact analysis, Test plan, Effort estimation, Project roadmap, Backlogs - creation & maintenance, Root cause analysis(RCA), Risk management strategy, Risk mitigation plan and Asset management strategy",
        "BA validations – AS IS – TO BE method, identify user stories without loopholes, break down to tasks, review test scope, test cases, perform functional, system, 
load and stress testing, Regression, User Acceptance Testing (UAT) & PROD checks, Fitness page preparation & validation as per Behavior Driven Development (BDD), automation of testing and processes using Excel and SQL",
        "Agile methodology – practice agile ceremonies like Daily scrum, Sprint planning, review & demo, retrospective, track all tasks & activities on JIRA by Epic, Story & Task, backlog maintenance, JIRA board cleanup, HP Application Lifecycle Management tool (ALM) version 11",
        "Data analytics – analyzed historical data, understood data trends and patterns, forecast data using excel, strategize the data monitoring & reporting on client 
portfolio based on risk parameters",
        "Data profiling – understand and identify the type and nature of data, build/ track/maintain Data mapping (between source and intermediate systems to destination) information, maintain Data dictionary",
        "Data flow – secure data access, limited access as per entitlements, authentication, transfer, loading, Staging, Cleansing, Data transformation (between source and target)",
        "Data integration (external data merging into existing dataset, prepare for DWH cube, archival and retention policies",
        "Data management – understood and monitored master and transaction tables, audit tables, parameter tables and history tables, considering referential integrity and avoiding duplicates. Archive the historical data without much loss, readily accessible and available for future use",
        "Data reporting and Reconciliation reporting – for risk assessment and constant monitoring"
      ],
      "skills": [
        {
          "name": "SQL",
          "proficiency_level": "Advanced",
          "years_of_experience": null
        },
        {
          "name": "Excel",
          "proficiency_level": "Advanced",
          "years_of_experience": null
        },
        {
          "name": "Agile methodology",
          "proficiency_level": "Advanced",
          "years_of_experience": null
        },
        {
          "name": "JIRA",
          "proficiency_level": "Advanced",
          "years_of_experience": null
        },
        {
          "name": "Data Analytics",
          "proficiency_level": "Advanced",
          "years_of_experience": null
        }
      ]
    },
    {
      "job_title": "Responsibilities Continuation",
      "company_name": "Cap Gemini",
      "start_date": null,
      "end_date": null,
      "location": null,
      "responsibilities": [
        "Requirements & Process Documentation –Conducted requirements elicitation sessions with end users of existing application, got the doubts and questions clarified from end users, involved in the creation of Business Requirement Document and Process Flow diagrams, Mockup screens for User review, Workflow diagrams for each type and group of user",
        "Design Analysis – Analyzed the SAP BO report design, identified the DB tables involved and joins created"
      ],
      "skills": null
    }
  ],
  "roles": null,
  "skills": [
    {
      "name": "Requirements Management: ALM, JIRA, Confluence",
      "proficiency_level": null,
      "years_of_experience": null
    },
    {
      "name": "Software Languages: VB, C++, Java – analyze code and interpret logic",
      "proficiency_level": null,
      "years_of_experience": null
    },
    {
      "name": "Office Tools: Word, Excel, PowerPoint, Visio, Outlook, SharePoint",
      "proficiency_level": null,
      "years_of_experience": null
    },
    {
      "name": "Excel – pivots, macros, lookups, lists, charts, VBA macros, data analytics",
      "proficiency_level": null,
      "years_of_experience": null
    },
    {
      "name": "Database – SQL, MySQL, Snowflake, PostgreSQL, Oracle, Teradata, Big Query",
      "proficiency_level": null,
      "years_of_experience": null
    },
    {
      "name": "Data visualization – Tableau and Spotfire",
      "proficiency_level": null,
      "years_of_experience": null
    }
  ],
  "soft_skills": null,
  "certifications": null,
  "languages": null,
  "projects": null,
  "references": null,
  "hobbies_interests": null
}

Please fill in any missing fields or correct any errors based on the image, while preserving the existing data.
R: {
  "personal_info": null,
  "summary": null,
  "education": null,
  "work_experience": [
    {
      "job_title": "Senior Consultant",
      "company_name": "Capco Technologies",
      "start_date": "Jan 2018",
      "end_date": "May 2018",
      "location": "Bengaluru, India",
      "responsibilities": [
        "Data analysis – expertise in writing medium to complex SQL queries (PostgreSQL), Handle Large volumes of data (in TB), ETL testing, Proficiency in Excel - pivots, charts, lists, lookup & ad-hoc analysis, VBA macro (using functions & forms)",
        "Data automation – can prepare excel based templates and processes to automate data reporting using Excel and other tools.",
        "Extensive experience on Tools - Microsoft Word, Excel, Visio, PowerPoint, SharePoint, Outlook, Teams, JIRA, Confluence",
        "Request and Coordination with External teams – handled using Service Now and Unity Portal",
        "Lead 15-member team, allocate tasks, review team’s efforts, track and monitor progress, holiday plans for team, define KPI for the team, performance review, hire team members, maintain good rapport and mentor the team, build confidence, reliability & assurance from stakeholders"
      ],
      "skills": null
    },
    {
      "job_title": "Senior Business Analyst",
      "company_name": "Wipro Technology (contract)",
      "start_date": "Apr 2019",
      "end_date": "May 2021",
      "location": "Bengaluru, India",
      "responsibilities": [
        "Being part of Change the Bank (CTB) team, prepared Functional Specification documentation, Business Requirements document, Impact analysis, created and maintained data/process flow chart using Visio, verified system run charts and prepared strategy planning document",
        "Agile technology – practiced agile ceremonies like Daily Stand up, monitored JIRA dashboard by Epic, Story & Task, backlog maintenance",
        "Product Owner - Gather requirements for the data and reporting team from US SMEs–Connect to source, perform Data profiling, Ingested data (using ADF) in required format to client data-warehouse (snowflake), Data mapping for the dashboard in Tableau, performed analytics on the marketing and sales metrics for the client Xerox.", 
        "Tableau analytics - Analyzed data trends, highlighted outliers, provided easily adjustable and relevant parameter filters for the tableau dashboards, performed 
data mining on structured data on ad-hoc basis.",
        "Experience working on HR data for sourcing, process automation, data masking and reporting solution.",
        "SaaS Cloud based solution – helped build ETL pipelines (shell scripts, Talend configuration), Ingested data (using Azure Data Factory ADF) in required format to cloud (wrote SQL like queries and analyzed on snowflake and Big query), and maintained client data-warehouse.",
        "Experience in creation and maintenance of Tableau Dashboard with visualizations, (source configuration, real time & extract, Dashboards, customized Filters) using data extracts and real-time data in large volumes in Terabytes and ability to work with multiple data sources and experience in creating interactive Dashboards with Actions, Parameter controls and Filters",
        "Data security - Enabled access restrictions on users to the Dashboard, as per the access level set.",
        "Performed Cloud migration testing and automated validation using Excel and SQL",
        "Have used Tableau Desktop 2019 for creating visualizations and Tableau server for configuring access control and sharing the dashboard to users.",
        "Collaborate with multiple teams – Production, Support, Architect, Development, Test teams, to implement the client requirement as expected"
      ],
      "skills": [
        {
          "name": "Tableau Desktop",
          "proficiency_level": "Advanced",
          "years_of_experience": 3.0
        },
        {
          "name": "SQL",
          "proficiency_level": "Advanced",
          "years_of_experience": 5.0
        },
        {
          "name": "Azure Data Factory",
          "proficiency_level": "Intermediate",
          "years_of_experience": 2.0
        },
        {
          "name": "Snowflake",
          "proficiency_level": "Intermediate",
          "years_of_experience": 2.0
        },
        {
          "name": "Big Query",
          "proficiency_level": "Intermediate",
          "years_of_experience": 2.0
        }
      ]
    },
    {
      "job_title": "Senior Consultant",
      "company_name": "Capco Technologies",
      "start_date": "Jan 2018",
      "end_date": "May 2018",
      "location": null,
      "responsibilities": [
        "Data analysis – expertise in writing medium to complex SQL queries (PostgreSQL), Handle Large volumes of data (in TB), ETL testing, Proficiency in Excel - pivots, charts, lists, lookup & ad-hoc analysis, VBA macro (using functions & forms)",
        "Data automation – can prepare excel based templates and processes to automate data reporting using Excel and other tools.",
        "Extensive experience on Tools - Microsoft Word, Excel, Visio, PowerPoint, SharePoint, Outlook, Teams, JIRA, Confluence",
        "Request and Coordination with External teams – handled using Service Now and Unity Portal",
        "Lead 15-member team, allocate tasks, review team’s efforts, track and monitor progress, holiday plans for team, define KPI for the team, performance review, hire team members, maintain good rapport and mentor the team, build confidence, reliability & assurance from stakeholders"
      ],
      "skills": null
    }
  ],
  "roles": null,
  "skills": null,
  "soft_skills": null,
  "certifications": null,
  "languages": null,
  "projects": null,
  "references": null,
  "hobbies_interests": null
}
---------------------------------------------------------------------

Datos acumulados antes de la fusión: {
  "personal_info": {
    "full_name": "UMA SEKARAN",
    "country": null,
    "city": null,
    "address": null,
    "phone_number": "****** 394 6602",
    "email": "<EMAIL>",
    "linkedin_profile": null,
    "website": null
  },
  "summary": "I am an IT Business Data Analyst with 16 years of Techno functional experience in enabling software and analytical solutions to Investment Banking domain, 
Printing and HR domain. Gained 6+ months experience working in US for clients like US Bank and American Crystal Sugar. Have acquired diversified experience in Consulting, Agile, Data strategy and integration, Data analysis using SQL and Excel, Operations management, VBA macros, Data mining and analytics using Tableau, enabling SaaS solution with Cloud data migration validation, API flow check, Requirements gathering, Testing, Stakeholder management and Production support. Have valid L2S visa till Aug 2026 to work remotely or hybrid from nearby city (Fargo, ND) of Jamestown, North Dakota – USA.",
  "education": null,
  "work_experience": [
    {
      "job_title": "Technical Consultant, Platform Test Analyst",
      "company_name": "Apex systems inc, Chicago",
      "start_date": "July 2024",
      "end_date": "Present",
      "location": null,
      "responsibilities": [
        "Process Documentation & Validation – got involved in validating the process, following the data mapping documents, functional validation, reporting and tracking to closure of the Reconciliation system for the Regulatory reporting data between Trade booking systems (Wall Street and Calypso) and DTCC Hub reporting system",       
        "Performed UAT on GUI platform developed for Asset management for US bank, by asset class type– aggregated and individual detail, grouping and filtration based on reporting filter values, authorization and authentication",
        "Wall Street – Performed FX trade booking (Spot, Forward, NDF, Swaps), ensured test coverage, verified the trade details on the DB tables involved and validated 
them in the new SDR reporting solution",
        "Performed validations of trades/XML reported on DTCC hub and in-house reporting platform for Trade state, RT and valuation messages"
      ],
      "skills": [
        {
          "name": "Wall Street 5.1.4, Calypso 17, DTCC hub, Postgres, MS SQL server, SQL, Excel",
          "proficiency_level": null,
          "years_of_experience": null
        }
      ]
    },
    {
      "job_title": "Business Analyst",
      "company_name": "Pro Source, Fargo, North Dakota",
      "start_date": "May 2024",
      "end_date": "June 2024",
      "location": null,
      "responsibilities": [
        "The project was on building a cloud based application and migrating data from a legacy mainframe application with Oracle DB and SAP BO reports, that served as a beet procurement, storage, seed distribution, contracts, acres associated, invoices, etc., for American Crystal Sugar, ND"
      ],
      "skills": []
    },
    {
      "job_title": "Lead Business Analyst",
      "company_name": "Cap Gemini",
      "start_date": "Dec 2021",
      "end_date": "Aug 2023",
      "location": "Bengaluru, India",
      "responsibilities": [
        "Stakeholder co-ordination– organized calls, elicit requirements, conducted workshops with showcasing prototypes, gave demo on new enhanced application before UAT, prepared PPT slides for sprint & quarterly review, share weekly updates to Product owner and Sponsors, coordinated with SMEs, vendors, external & cross functional teams, Development team, proactive anticipation of issues",
        "Product Owner – performed the role for implementing specific features, track and ensure on-time delivery of new features/enhancements/bug fix, within timelines 
and budget estimated based on FTEs (Full Time Equivalent), practiced Six sigma methodology",
        "Release Management –perform UAT validations, timeline Test evidence , record keeping, Demo to clients, Plan release steps with DEV & Testing team",
        "Requirements & Process Document –Creation and maintenance of Business Requirement Document, Data strategy and Process Flow diagrams, Entity Relationship(ER) diagram, End to end design & review of Data flow architecture diagram, Mockup screens for User review, Workflow diagrams for each type and group of user",
        "Impact analysis, Test plan, Effort estimation, Project roadmap, Backlogs - creation & maintenance, Root cause analysis(RCA), Risk management strategy, Risk mitigation plan and Asset management strategy",
        "BA validations – AS IS – TO BE method, identify user stories without loopholes, break down to tasks, review test scope, test cases, perform functional, system, 
load and stress testing, Regression, User Acceptance Testing (UAT) & PROD checks, Fitness page preparation & validation as per Behavior Driven Development (BDD), automation of testing and processes using Excel and SQL",
        "Agile methodology – practice agile ceremonies like Daily scrum, Sprint planning, review & demo, retrospective, track all tasks & activities on JIRA by Epic, Story & Task, backlog maintenance, JIRA board cleanup, HP Application Lifecycle Management tool (ALM) version 11",
        "Data analytics – analyzed historical data, understood data trends and patterns, forecast data using excel, strategize the data monitoring & reporting on client 
portfolio based on risk parameters",
        "Data profiling – understand and identify the type and nature of data, build/ track/maintain Data mapping (between source and intermediate systems to destination) information, maintain Data dictionary",
        "Data flow – secure data access, limited access as per entitlements, authentication, transfer, loading, Staging, Cleansing, Data transformation (between source and target)",
        "Data integration (external data merging into existing dataset, prepare for DWH cube, archival and retention policies",
        "Data management – understood and monitored master and transaction tables, audit tables, parameter tables and history tables, considering referential integrity and avoiding duplicates. Archive the historical data without much loss, readily accessible and available for future use",
        "Data reporting and Reconciliation reporting – for risk assessment and constant monitoring"
      ],
      "skills": [
        {
          "name": "SQL",
          "proficiency_level": "Advanced",
          "years_of_experience": null
        },
        {
          "name": "Excel",
          "proficiency_level": "Advanced",
          "years_of_experience": null
        },
        {
          "name": "Agile methodology",
          "proficiency_level": "Advanced",
          "years_of_experience": null
        },
        {
          "name": "JIRA",
          "proficiency_level": "Advanced",
          "years_of_experience": null
        },
        {
          "name": "Data Analytics",
          "proficiency_level": "Advanced",
          "years_of_experience": null
        }
      ]
    },
    {
      "job_title": "Responsibilities Continuation",
      "company_name": "Cap Gemini",
      "start_date": null,
      "end_date": null,
      "location": null,
      "responsibilities": [
        "Requirements & Process Documentation –Conducted requirements elicitation sessions with end users of existing application, got the doubts and questions clarified from end users, involved in the creation of Business Requirement Document and Process Flow diagrams, Mockup screens for User review, Workflow diagrams for each type and group of user",
        "Design Analysis – Analyzed the SAP BO report design, identified the DB tables involved and joins created"
      ],
      "skills": null
    }
  ],
  "roles": null,
  "skills": [
    {
      "name": "Requirements Management: ALM, JIRA, Confluence",
      "proficiency_level": null,
      "years_of_experience": null
    },
    {
      "name": "Software Languages: VB, C++, Java – analyze code and interpret logic",
      "proficiency_level": null,
      "years_of_experience": null
    },
    {
      "name": "Office Tools: Word, Excel, PowerPoint, Visio, Outlook, SharePoint",
      "proficiency_level": null,
      "years_of_experience": null
    },
    {
      "name": "Excel – pivots, macros, lookups, lists, charts, VBA macros, data analytics",
      "proficiency_level": null,
      "years_of_experience": null
    },
    {
      "name": "Database – SQL, MySQL, Snowflake, PostgreSQL, Oracle, Teradata, Big Query",
      "proficiency_level": null,
      "years_of_experience": null
    },
    {
      "name": "Data visualization – Tableau and Spotfire",
      "proficiency_level": null,
      "years_of_experience": null
    }
  ],
  "soft_skills": null,
  "certifications": null,
  "languages": null,
  "projects": null,
  "references": null,
  "hobbies_interests": null
}

Nuevos datos de la página 3: {
  "personal_info": null,
  "summary": null,
  "education": null,
  "work_experience": [
    {
      "job_title": "Senior Consultant",
      "company_name": "Capco Technologies",
      "start_date": "Jan 2018",
      "end_date": "May 2018",
      "location": "Bengaluru, India",
      "responsibilities": [
        "Data analysis – expertise in writing medium to complex SQL queries (PostgreSQL), Handle Large volumes of data (in TB), ETL testing, Proficiency in Excel - pivots, charts, lists, lookup & ad-hoc analysis, VBA macro (using functions & forms)",
        "Data automation – can prepare excel based templates and processes to automate data reporting using Excel and other tools.",
        "Extensive experience on Tools - Microsoft Word, Excel, Visio, PowerPoint, SharePoint, Outlook, Teams, JIRA, Confluence",
        "Request and Coordination with External teams – handled using Service Now and Unity Portal",
        "Lead 15-member team, allocate tasks, review team’s efforts, track and monitor progress, holiday plans for team, define KPI for the team, performance review, hire team members, maintain good rapport and mentor the team, build confidence, reliability & assurance from stakeholders"
      ],
      "skills": null
    },
    {
      "job_title": "Senior Business Analyst",
      "company_name": "Wipro Technology (contract)",
      "start_date": "Apr 2019",
      "end_date": "May 2021",
      "location": "Bengaluru, India",
      "responsibilities": [
        "Being part of Change the Bank (CTB) team, prepared Functional Specification documentation, Business Requirements document, Impact analysis, created and maintained data/process flow chart using Visio, verified system run charts and prepared strategy planning document",
        "Agile technology – practiced agile ceremonies like Daily Stand up, monitored JIRA dashboard by Epic, Story & Task, backlog maintenance",
        "Product Owner - Gather requirements for the data and reporting team from US SMEs–Connect to source, perform Data profiling, Ingested data (using ADF) in required format to client data-warehouse (snowflake), Data mapping for the dashboard in Tableau, performed analytics on the marketing and sales metrics for the client Xerox.", 
        "Tableau analytics - Analyzed data trends, highlighted outliers, provided easily adjustable and relevant parameter filters for the tableau dashboards, performed 
data mining on structured data on ad-hoc basis.",
        "Experience working on HR data for sourcing, process automation, data masking and reporting solution.",
        "SaaS Cloud based solution – helped build ETL pipelines (shell scripts, Talend configuration), Ingested data (using Azure Data Factory ADF) in required format to cloud (wrote SQL like queries and analyzed on snowflake and Big query), and maintained client data-warehouse.",
        "Experience in creation and maintenance of Tableau Dashboard with visualizations, (source configuration, real time & extract, Dashboards, customized Filters) using data extracts and real-time data in large volumes in Terabytes and ability to work with multiple data sources and experience in creating interactive Dashboards with Actions, Parameter controls and Filters",
        "Data security - Enabled access restrictions on users to the Dashboard, as per the access level set.",
        "Performed Cloud migration testing and automated validation using Excel and SQL",
        "Have used Tableau Desktop 2019 for creating visualizations and Tableau server for configuring access control and sharing the dashboard to users.",
        "Collaborate with multiple teams – Production, Support, Architect, Development, Test teams, to implement the client requirement as expected"
      ],
      "skills": [
        {
          "name": "Tableau Desktop",
          "proficiency_level": "Advanced",
          "years_of_experience": 3.0
        },
        {
          "name": "SQL",
          "proficiency_level": "Advanced",
          "years_of_experience": 5.0
        },
        {
          "name": "Azure Data Factory",
          "proficiency_level": "Intermediate",
          "years_of_experience": 2.0
        },
        {
          "name": "Snowflake",
          "proficiency_level": "Intermediate",
          "years_of_experience": 2.0
        },
        {
          "name": "Big Query",
          "proficiency_level": "Intermediate",
          "years_of_experience": 2.0
        }
      ]
    },
    {
      "job_title": "Senior Consultant",
      "company_name": "Capco Technologies",
      "start_date": "Jan 2018",
      "end_date": "May 2018",
      "location": null,
      "responsibilities": [
        "Data analysis – expertise in writing medium to complex SQL queries (PostgreSQL), Handle Large volumes of data (in TB), ETL testing, Proficiency in Excel - pivots, charts, lists, lookup & ad-hoc analysis, VBA macro (using functions & forms)",
        "Data automation – can prepare excel based templates and processes to automate data reporting using Excel and other tools.",
        "Extensive experience on Tools - Microsoft Word, Excel, Visio, PowerPoint, SharePoint, Outlook, Teams, JIRA, Confluence",
        "Request and Coordination with External teams – handled using Service Now and Unity Portal",
        "Lead 15-member team, allocate tasks, review team’s efforts, track and monitor progress, holiday plans for team, define KPI for the team, performance review, hire team members, maintain good rapport and mentor the team, build confidence, reliability & assurance from stakeholders"
      ],
      "skills": null
    }
  ],
  "roles": null,
  "skills": null,
  "soft_skills": null,
  "certifications": null,
  "languages": null,
  "projects": null,
  "references": null,
  "hobbies_interests": null
}

Datos fusionados: {
  "personal_info": {
    "full_name": "UMA SEKARAN",
    "country": null,
    "city": null,
    "address": null,
    "phone_number": "****** 394 6602",
    "email": "<EMAIL>",
    "linkedin_profile": null,
    "website": null
  },
  "summary": "I am an IT Business Data Analyst with 16 years of Techno functional experience in enabling software and analytical solutions to Investment Banking domain, 
Printing and HR domain. Gained 6+ months experience working in US for clients like US Bank and American Crystal Sugar. Have acquired diversified experience in Consulting, Agile, Data strategy and integration, Data analysis using SQL and Excel, Operations management, VBA macros, Data mining and analytics using Tableau, enabling SaaS solution with Cloud data migration validation, API flow check, Requirements gathering, Testing, Stakeholder management and Production support. Have valid L2S visa till Aug 2026 to work remotely or hybrid from nearby city (Fargo, ND) of Jamestown, North Dakota – USA.",
  "education": null,
  "work_experience": [
    {
      "job_title": "Technical Consultant, Platform Test Analyst",
      "company_name": "Apex systems inc, Chicago",
      "start_date": "July 2024",
      "end_date": "Present",
      "location": null,
      "responsibilities": [
        "Process Documentation & Validation – got involved in validating the process, following the data mapping documents, functional validation, reporting and tracking to closure of the Reconciliation system for the Regulatory reporting data between Trade booking systems (Wall Street and Calypso) and DTCC Hub reporting system",       
        "Performed UAT on GUI platform developed for Asset management for US bank, by asset class type– aggregated and individual detail, grouping and filtration based on reporting filter values, authorization and authentication",
        "Wall Street – Performed FX trade booking (Spot, Forward, NDF, Swaps), ensured test coverage, verified the trade details on the DB tables involved and validated 
them in the new SDR reporting solution",
        "Performed validations of trades/XML reported on DTCC hub and in-house reporting platform for Trade state, RT and valuation messages"
      ],
      "skills": [
        {
          "name": "Wall Street 5.1.4, Calypso 17, DTCC hub, Postgres, MS SQL server, SQL, Excel",
          "proficiency_level": null,
          "years_of_experience": null
        }
      ]
    },
    {
      "job_title": "Business Analyst",
      "company_name": "Pro Source, Fargo, North Dakota",
      "start_date": "May 2024",
      "end_date": "June 2024",
      "location": null,
      "responsibilities": [
        "The project was on building a cloud based application and migrating data from a legacy mainframe application with Oracle DB and SAP BO reports, that served as a beet procurement, storage, seed distribution, contracts, acres associated, invoices, etc., for American Crystal Sugar, ND"
      ],
      "skills": []
    },
    {
      "job_title": "Lead Business Analyst",
      "company_name": "Cap Gemini",
      "start_date": "Dec 2021",
      "end_date": "Aug 2023",
      "location": "Bengaluru, India",
      "responsibilities": [
        "Stakeholder co-ordination– organized calls, elicit requirements, conducted workshops with showcasing prototypes, gave demo on new enhanced application before UAT, prepared PPT slides for sprint & quarterly review, share weekly updates to Product owner and Sponsors, coordinated with SMEs, vendors, external & cross functional teams, Development team, proactive anticipation of issues",
        "Product Owner – performed the role for implementing specific features, track and ensure on-time delivery of new features/enhancements/bug fix, within timelines 
and budget estimated based on FTEs (Full Time Equivalent), practiced Six sigma methodology",
        "Release Management –perform UAT validations, timeline Test evidence , record keeping, Demo to clients, Plan release steps with DEV & Testing team",
        "Requirements & Process Document –Creation and maintenance of Business Requirement Document, Data strategy and Process Flow diagrams, Entity Relationship(ER) diagram, End to end design & review of Data flow architecture diagram, Mockup screens for User review, Workflow diagrams for each type and group of user",
        "Impact analysis, Test plan, Effort estimation, Project roadmap, Backlogs - creation & maintenance, Root cause analysis(RCA), Risk management strategy, Risk mitigation plan and Asset management strategy",
        "BA validations – AS IS – TO BE method, identify user stories without loopholes, break down to tasks, review test scope, test cases, perform functional, system, 
load and stress testing, Regression, User Acceptance Testing (UAT) & PROD checks, Fitness page preparation & validation as per Behavior Driven Development (BDD), automation of testing and processes using Excel and SQL",
        "Agile methodology – practice agile ceremonies like Daily scrum, Sprint planning, review & demo, retrospective, track all tasks & activities on JIRA by Epic, Story & Task, backlog maintenance, JIRA board cleanup, HP Application Lifecycle Management tool (ALM) version 11",
        "Data analytics – analyzed historical data, understood data trends and patterns, forecast data using excel, strategize the data monitoring & reporting on client 
portfolio based on risk parameters",
        "Data profiling – understand and identify the type and nature of data, build/ track/maintain Data mapping (between source and intermediate systems to destination) information, maintain Data dictionary",
        "Data flow – secure data access, limited access as per entitlements, authentication, transfer, loading, Staging, Cleansing, Data transformation (between source and target)",
        "Data integration (external data merging into existing dataset, prepare for DWH cube, archival and retention policies",
        "Data management – understood and monitored master and transaction tables, audit tables, parameter tables and history tables, considering referential integrity and avoiding duplicates. Archive the historical data without much loss, readily accessible and available for future use",
        "Data reporting and Reconciliation reporting – for risk assessment and constant monitoring"
      ],
      "skills": [
        {
          "name": "SQL",
          "proficiency_level": "Advanced",
          "years_of_experience": null
        },
        {
          "name": "Excel",
          "proficiency_level": "Advanced",
          "years_of_experience": null
        },
        {
          "name": "Agile methodology",
          "proficiency_level": "Advanced",
          "years_of_experience": null
        },
        {
          "name": "JIRA",
          "proficiency_level": "Advanced",
          "years_of_experience": null
        },
        {
          "name": "Data Analytics",
          "proficiency_level": "Advanced",
          "years_of_experience": null
        }
      ]
    },
    {
      "job_title": "Responsibilities Continuation",
      "company_name": "Cap Gemini",
      "start_date": null,
      "end_date": null,
      "location": null,
      "responsibilities": [
        "Requirements & Process Documentation –Conducted requirements elicitation sessions with end users of existing application, got the doubts and questions clarified from end users, involved in the creation of Business Requirement Document and Process Flow diagrams, Mockup screens for User review, Workflow diagrams for each type and group of user",
        "Design Analysis – Analyzed the SAP BO report design, identified the DB tables involved and joins created"
      ],
      "skills": null
    },
    {
      "job_title": "Senior Consultant",
      "company_name": "Capco Technologies",
      "start_date": "Jan 2018",
      "end_date": "May 2018",
      "location": "Bengaluru, India",
      "responsibilities": [
        "Data analysis – expertise in writing medium to complex SQL queries (PostgreSQL), Handle Large volumes of data (in TB), ETL testing, Proficiency in Excel - pivots, charts, lists, lookup & ad-hoc analysis, VBA macro (using functions & forms)",
        "Data automation – can prepare excel based templates and processes to automate data reporting using Excel and other tools.",
        "Extensive experience on Tools - Microsoft Word, Excel, Visio, PowerPoint, SharePoint, Outlook, Teams, JIRA, Confluence",
        "Request and Coordination with External teams – handled using Service Now and Unity Portal",
        "Lead 15-member team, allocate tasks, review team’s efforts, track and monitor progress, holiday plans for team, define KPI for the team, performance review, hire team members, maintain good rapport and mentor the team, build confidence, reliability & assurance from stakeholders"
      ],
      "skills": null
    },
    {
      "job_title": "Senior Business Analyst",
      "company_name": "Wipro Technology (contract)",
      "start_date": "Apr 2019",
      "end_date": "May 2021",
      "location": "Bengaluru, India",
      "responsibilities": [
        "Being part of Change the Bank (CTB) team, prepared Functional Specification documentation, Business Requirements document, Impact analysis, created and maintained data/process flow chart using Visio, verified system run charts and prepared strategy planning document",
        "Agile technology – practiced agile ceremonies like Daily Stand up, monitored JIRA dashboard by Epic, Story & Task, backlog maintenance",
        "Product Owner - Gather requirements for the data and reporting team from US SMEs–Connect to source, perform Data profiling, Ingested data (using ADF) in required format to client data-warehouse (snowflake), Data mapping for the dashboard in Tableau, performed analytics on the marketing and sales metrics for the client Xerox.", 
        "Tableau analytics - Analyzed data trends, highlighted outliers, provided easily adjustable and relevant parameter filters for the tableau dashboards, performed 
data mining on structured data on ad-hoc basis.",
        "Experience working on HR data for sourcing, process automation, data masking and reporting solution.",
        "SaaS Cloud based solution – helped build ETL pipelines (shell scripts, Talend configuration), Ingested data (using Azure Data Factory ADF) in required format to cloud (wrote SQL like queries and analyzed on snowflake and Big query), and maintained client data-warehouse.",
        "Experience in creation and maintenance of Tableau Dashboard with visualizations, (source configuration, real time & extract, Dashboards, customized Filters) using data extracts and real-time data in large volumes in Terabytes and ability to work with multiple data sources and experience in creating interactive Dashboards with Actions, Parameter controls and Filters",
        "Data security - Enabled access restrictions on users to the Dashboard, as per the access level set.",
        "Performed Cloud migration testing and automated validation using Excel and SQL",
        "Have used Tableau Desktop 2019 for creating visualizations and Tableau server for configuring access control and sharing the dashboard to users.",
        "Collaborate with multiple teams – Production, Support, Architect, Development, Test teams, to implement the client requirement as expected"
      ],
      "skills": [
        {
          "name": "Tableau Desktop",
          "proficiency_level": "Advanced",
          "years_of_experience": 3.0
        },
        {
          "name": "SQL",
          "proficiency_level": "Advanced",
          "years_of_experience": 5.0
        },
        {
          "name": "Azure Data Factory",
          "proficiency_level": "Intermediate",
          "years_of_experience": 2.0
        },
        {
          "name": "Snowflake",
          "proficiency_level": "Intermediate",
          "years_of_experience": 2.0
        },
        {
          "name": "Big Query",
          "proficiency_level": "Intermediate",
          "years_of_experience": 2.0
        }
      ]
    }
  ],
  "roles": null,
  "skills": [
    {
      "name": "Requirements Management: ALM, JIRA, Confluence",
      "proficiency_level": null,
      "years_of_experience": null
    },
    {
      "name": "Software Languages: VB, C++, Java – analyze code and interpret logic",
      "proficiency_level": null,
      "years_of_experience": null
    },
    {
      "name": "Office Tools: Word, Excel, PowerPoint, Visio, Outlook, SharePoint",
      "proficiency_level": null,
      "years_of_experience": null
    },
    {
      "name": "Excel – pivots, macros, lookups, lists, charts, VBA macros, data analytics",
      "proficiency_level": null,
      "years_of_experience": null
    },
    {
      "name": "Database – SQL, MySQL, Snowflake, PostgreSQL, Oracle, Teradata, Big Query",
      "proficiency_level": null,
      "years_of_experience": null
    },
    {
      "name": "Data visualization – Tableau and Spotfire",
      "proficiency_level": null,
      "years_of_experience": null
    }
  ],
  "soft_skills": null,
  "certifications": null,
  "languages": null,
  "projects": null,
  "references": null,
  "hobbies_interests": null
}
Actualizando datos con información de la página 3
