import psutil
import time
import os
from fastapi import APIRouter, Request

router = APIRouter()

@router.get("/health", summary="Health Check Endpoint")
async def health_check(request: Request):
    """
    Health check endpoint to monitor system metrics and show tasks in line or being processed.
    """
    active_tasks = request.app.state.active_tasks

    # Usage statistics
    cpu_percent = psutil.cpu_percent(interval=1)
    memory_info = psutil.virtual_memory()
    memory_percent = memory_info.percent

    # Tasks currently being processed
    processing_tasks = []
    for task, metadata in active_tasks.items():
        if not task.done() and not task.cancelled():
            # Calculate task age
            start_time = metadata.get("start_time", time.time())
            task_age = time.time() - start_time

            processing_tasks.append({
                "task_id": metadata.get("task_id", id(task)),
                "action": metadata["action"],
                "running_time_seconds": round(task_age, 2),
                "file_name": metadata.get("file_name", "Unknown")
            })

    # Waiting tasks are calculated by subtracting the semaphore capacity from the number of active tasks
    default_capacity = int(os.getenv("MAX_CONCURRENT_TASKS", "4"))
    semaphore_capacity = getattr(request.app.state.semaphore, "_value", default_capacity)
    waiting_tasks_count = max(0, len(active_tasks) - semaphore_capacity)

    return {
        "status": "ok",
        "version": "4.9.25.12.12",
        "message": "Service is running",
        "system_metrics": {
            "cpu_usage_percent": cpu_percent,
            "memory_usage_percent": memory_percent,
        },
        "tasks": {
            "processing_count": len(processing_tasks),
            "processing_details": processing_tasks,
            "waiting_count": waiting_tasks_count,
        },
    }

